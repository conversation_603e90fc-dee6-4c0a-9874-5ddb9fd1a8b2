<?php

namespace App\Console\Commands;

use App\Models\Response;
use App\Models\Survey;
use Carbon\Carbon;
use Illuminate\Console\Command;

class FlagExpiredResponses extends Command
{
    protected $signature = 'responses:flag-expired';
    protected $description = 'Flag incomplete responses as submitted when their survey expires';

    public function handle()
    {
        $today = now()->format('Y-m-d');
        
        $expiredSurveys = Survey::whereDate('end_date', '<=', $today)
            ->whereNotNull('end_date')
            ->with(['responses' => function($query) {
                $query->whereNull('submitted_at');
            }])
            ->get();

        $updatedCount = 0;

        foreach ($expiredSurveys as $survey) {
            $count = $survey->responses()
                ->whereNull('submitted_at')
                ->update([
                    'submitted_at' => $survey->end_date
                ]);
                
            $updatedCount += $count;
        }

        $this->info("Marked $updatedCount responses as submitted from expired surveys.");
        
        return Command::SUCCESS;
    }
}
