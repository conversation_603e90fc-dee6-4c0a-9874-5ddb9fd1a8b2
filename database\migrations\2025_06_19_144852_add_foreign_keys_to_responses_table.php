<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('responses', function (Blueprint $table) {
            $table->foreign(['respondent_id'], 'responses_ibfk_1')->references(['id'])->on('respondents')->onUpdate('restrict')->onDelete('restrict');
            $table->foreign(['survey_id'], 'responses_ibfk_2')->references(['id'])->on('surveys')->onUpdate('restrict')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('responses', function (Blueprint $table) {
            $table->dropForeign('responses_ibfk_1');
            $table->dropForeign('responses_ibfk_2');
        });
    }
};
