<?php

namespace App\Exports;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ExportDataUser implements FromArray, WithHeadings, WithTitle, ShouldAutoSize, WithEvents
{
    public function array(): array
    {
        $data = [
            ['Garit', '<EMAIL>', 'superadmin', '1'],
            ['Subur', '<EMAIL>', 'pengelola', '1'],
            ['Bintang', '<EMAIL>', 'responden_anggota', '1'],
            ['Dewi', '<EMAIL>', 'superadmin', '0'],
        ];
        return $data;
    }

    public function headings(): array
    {
        return [
            ['TEMPLATE IMPOR DATA PENGGUNA'],
            [],
            [
                'Nama',
                'Email',
                'Role',
                'Status'
            ]
        ];
    }

    public function title(): string
    {
        return 'Template';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->mergeCells('A1:D1');
                $event->sheet->getStyle('A1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 14,
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                    ],
                ]);
                $event->sheet->getStyle('A3:D3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF'],
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '4472C4'],
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => '000000'],
                        ],
                    ],
                ]);
                $dataRowCount = count($this->array());
                if ($dataRowCount > 0) {
                    $event->sheet->getStyle('A4:D' . ($dataRowCount + 3))->applyFromArray([
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000'],
                            ],
                        ],
                    ]);
                }
                $event->sheet->getColumnDimension('A')->setWidth(20);
                $event->sheet->getColumnDimension('B')->setWidth(30);
                $event->sheet->getColumnDimension('C')->setWidth(25);
                $event->sheet->getColumnDimension('D')->setWidth(10);

                $instructionRow = $dataRowCount + 5;
                $event->sheet->setCellValue('A' . $instructionRow, 'Petunjuk:');
                $event->sheet->setCellValue('A' . ($instructionRow + 1), '- Jangan mengubah posisi tabel');
                $event->sheet->setCellValue('A' . ($instructionRow + 2), '- Jangan mengubah nama kolom header');
                $event->sheet->setCellValue('A' . ($instructionRow + 3), '- Pastikan format file tetap .xlsx');
                $event->sheet->setCellValue('A' . ($instructionRow + 4), '- Data yang diimpor akan divalidasi sesuai format');
                $event->sheet->setCellValue('A' . ($instructionRow + 5), '- Kolom status harus diisi dengan 1(aktif) atau 0(nonaktif)');
            },
        ];
    }
}