<script setup lang="ts">
import { onMounted, nextTick, ref, computed, watch } from "vue";
import { Head, usePage, useForm, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { KTDataTable } from "@/../metronic/core/components/datatable/datatable";
import Datatable from "@/Components/Partials/Datatable.vue";
import Toast from "@/Components/Partials/Toast.vue";
import ConfirmationDialog from "./Partials/ConfirmationDialog.vue";
import ReferenceDataFormModal from "./Partials/ReferenceDataFormModal.vue";
import ReferenceDataImportModal from "./Partials/ReferenceDataImportModal.vue";

const page = usePage();
const title = computed(() => (typeof page.props.title === 'string' && page.props.title ? page.props.title : 'Detail') as string);
const parentKey = computed(() => page.props.key as string);

let datatable: KTDataTable<any> | null = null;
const toastRef = ref();
const codeInputRef = ref<HTMLInputElement | null>(null);
const showTrashedDetails = ref(false);
const showImportModal = ref(false);

const showDetailModal = ref(false);
const isEditDetailMode = ref(false);
const editingDetailId = ref<string | number | null>(null);

const detailForm = useForm({
  ref_code: "",
  ref_name: "",
  ref_desc: "",
  parent_data: "",
  link_parent: false,
});

const confirmDetailDeleteDialog = ref({
  show: false,
  title: "",
  message: "",
  onConfirm: () => {},
});

// Flash messages
const flashMessage = computed(() => page.props.flash?.success || null);
const flashError = computed(() => page.props.flash?.error || null);

function openAddDetailModal() {
  isEditDetailMode.value = false;
  editingDetailId.value = null;
  detailForm.reset();
  detailForm.clearErrors();
  showDetailModal.value = true;
  setTimeout(() => {
    codeInputRef.value?.focus();
  }, 50);
}

function openEditDetailModal(item: any) {
  isEditDetailMode.value = true;
  editingDetailId.value = item.id;
  detailForm.reset();
  detailForm.clearErrors();
  detailForm.ref_code = item.ref_code;
  detailForm.ref_name = item.ref_name;
  detailForm.ref_desc = item.ref_desc || "";
  showDetailModal.value = true;
}

function closeDetailModal() {
  showDetailModal.value = false;
  detailForm.reset();
  detailForm.clearErrors();
}

function validateDetailForm() {
  let isValid = true;
  detailForm.clearErrors();
  
  if (!detailForm.ref_code) {
    detailForm.setError('ref_code', 'Kode wajib diisi');
    isValid = false;
  }
  
  if (!detailForm.ref_name) {
    detailForm.setError('ref_name', 'Nama wajib diisi.');
    isValid = false;
  } else if (detailForm.ref_name.length < 2) {
    detailForm.setError('ref_name', 'Nama minimal 2 karakter.');
    isValid = false;
  }
  
  return isValid;
}

function submitDetailForm() {
  if (!parentKey.value) {
    console.error("Parent key is missing");
    return;
  }

  if (!validateDetailForm()) {
    return;
  }

  const targetKey = detailForm.parent_data || parentKey.value;
  
  if (isEditDetailMode.value) {
    detailForm.put(route("reference-data.update-detail", { 
      key: parentKey.value, 
      detailId: editingDetailId.value 
    }), {
      onSuccess: () => {
        closeDetailModal();
        if (datatable) datatable.reload();
        toastRef.value?.show("success", "Detail item berhasil diperbarui.");
      },
      onError: (errors: any) => {
        console.error("Error updating detail:", errors);
        if (errors?.response?.status === 404) {
          toastRef.value?.show("error", "Data tidak ditemukan");
        } else if (errors?.ref_code && errors.ref_code.includes('already been taken')) {
          detailForm.setError('ref_code', 'Kode harus unik');
        } else {
          toastRef.value?.show("error", "Terjadi kesalahan pada sistem");
        }
      }
    });
  } else {
    detailForm.post(route("reference-data.store-detail", { key: targetKey }), {
      onSuccess: () => {
        closeDetailModal();
        if (datatable) datatable.reload();
        toastRef.value?.show("success", "Detail item berhasil ditambahkan.");
      },
      onError: (errors: any) => {
        console.error("Error adding detail:", errors);
        if (errors?.ref_code && errors.ref_code.includes('already been taken')) {
          detailForm.setError('ref_code', 'Kode harus unik');
        } else if (errors?.parent_data) {
          detailForm.setError('parent_data', errors.parent_data[0]);
        } else {
          toastRef.value?.show("error", "Terjadi kesalahan pada sistem");
        }
      }
    });
  }
}

function openImportModal() {
  showImportModal.value = true;
}

function closeImportModal() {
  showImportModal.value = false;
}

function handleImportSuccess() {
  if (datatable) {
    datatable.reload();
  }
}

function showConfirmDetailDeleteDialog(title: string, message: string, onConfirm: () => void) {
confirmDetailDeleteDialog.value = {
  show: true,
  title,
  message,
  onConfirm: () => {
    onConfirm();
    confirmDetailDeleteDialog.value.show = false;
  }
};
}

function closeConfirmDetailDeleteDialog() {
confirmDetailDeleteDialog.value.show = false;
}

function confirmDeleteDetail(item: any) {
if (!parentKey.value) {
  return;
}
showConfirmDetailDeleteDialog(
  "Konfirmasi Hapus",
  `Apakah Anda yakin ingin menghapus "${item.ref_name || item.ref_code}"?`,
  () => {
    router.delete(route("reference-data.destroy-detail", { key: parentKey.value, detailId: item.id }), {
      onSuccess: () => {
        if (datatable) datatable.reload();
        toastRef.value?.show("success", `"${item.ref_name || item.ref_code}" berhasil dihapus.`);
      },
      onError: (errors: any) => {
        console.error("Error deleting detail:", errors);
        if (errors?.response?.status === 404) {
          toastRef.value?.show("error", "Data tidak ditemukan");
        } else if (errors?.response?.status === 422) {
          toastRef.value?.show("error", "Gagal menghapus detail. Silakan coba lagi.");
        } else {
          toastRef.value?.show("error", "Terjadi kesalahan pada sistem");
        }
      },
    });
  }
);
}

function confirmRestoreDetail(item: any) {
if (!parentKey.value) {
  toastRef.value?.show("error", "Parent key is missing. Cannot restore.");
  return;
}
showConfirmDetailDeleteDialog(
  "Confirm Restore",
  `Are you sure you want to restore "${item.ref_name || item.ref_code}"?`,
  () => {
    router.patch(route("reference-data.restore-detail", { key: parentKey.value, detailId: item.id }), {}, {
      onSuccess: () => {
        if (datatable) datatable.reload();
        toastRef.value?.show("success", `"${item.ref_name || item.ref_code}" berhasil di restore.`);
      },
      onError: (errors: any) => {
        console.error("Error restoring detail:", errors);
        if (errors?.response?.status === 404) {
          toastRef.value?.show("error", "Data tidak ditemukan");
        } else {
          toastRef.value?.show("error", "Terjadi kesalahan pada sistem");
        }
      },
    });
  }
);
}

function confirmForceDeleteDetail(item: any) {
if (!parentKey.value) {
  toastRef.value?.show("error", "Parent key is missing. Cannot force delete.");
  return;
}
showConfirmDetailDeleteDialog(
  "Confirm Permanent Delete",
  `Are you sure you want to PERMANENTLY DELETE "${item.ref_name || item.ref_code}"? This action cannot be undone.`,
  () => {
    router.delete(route("reference-data.force-delete-detail", { key: parentKey.value, detailId: item.id }), {
      onSuccess: () => {
        if (datatable) datatable.reload();
        toastRef.value?.show("success", `"${item.ref_name || item.ref_code}" berhasil dihapus secara permanen.`);
      },
      onError: (errors: any) => {
        console.error("Error force deleting detail:", errors);
        if (errors?.response?.status === 404) {
          toastRef.value?.show("error", "Data tidak ditemukan");
        } else {
          toastRef.value?.show("error", "Terjadi kesalahan pada sistem");
        }
      },
    });
  }
);
}

function handleDetailTableAction(e: MouseEvent) {
const target = e.target as Element | null;
if (!target) return;

const editBtn = target.closest('[data-action="edit-detail"]');
const deleteBtn = target.closest('[data-action="delete-detail"]');
const restoreBtn = target.closest('[data-action="restore-detail"]');
const forceDeleteBtn = target.closest('[data-action="force-delete-detail"]');

if (editBtn) {
  const id = editBtn.getAttribute('data-id');
  const code = editBtn.getAttribute('data-code') || '';
  const name = editBtn.getAttribute('data-name') || '';
  const desc = editBtn.getAttribute('data-desc') || '';
  if (id) {
    openEditDetailModal({ id, ref_code: code, ref_name: name, ref_desc: desc });
  }
} else if (deleteBtn) {
  const id = deleteBtn.getAttribute('data-id');
  const name = deleteBtn.getAttribute('data-name') || deleteBtn.getAttribute('data-code') || 'this item';
  if (id) {
    confirmDeleteDetail({ id, ref_name: name });
  }
} else if (restoreBtn) {
  const id = restoreBtn.getAttribute('data-id');
  const name = restoreBtn.getAttribute('data-name') || restoreBtn.getAttribute('data-code') || 'this item';
  if (id) {
    confirmRestoreDetail({ id, ref_name: name });
  }
} else if (forceDeleteBtn) {
  const id = forceDeleteBtn.getAttribute('data-id');
  const name = forceDeleteBtn.getAttribute('data-name') || forceDeleteBtn.getAttribute('data-code') || 'this item';
  if (id) {
    confirmForceDeleteDetail({ id, ref_name: name });
  }
}
}

function initializeDatatable(retries = 10) {
if (!parentKey.value) {
  console.warn("[Detail] Parent key not available yet for datatable init. Retrying...");
  if (retries > 0) {
      setTimeout(() => initializeDatatable(retries - 1), 300);
  }
  return;
}
const dataUrl = route("reference-data.detail-data", {
  key: parentKey.value,
  show_trashed: showTrashedDetails.value ? 'all' : 'false'
});
const element = document.querySelector("#datatable-reference-detail");

if (!element && retries > 0) {
  setTimeout(() => initializeDatatable(retries - 1), 300);
  return;
}

if (element instanceof HTMLElement) {
  datatable = new KTDataTable(element, {
    infoEmpty: "Tidak ada data yang tersedia.",
    apiEndpoint: dataUrl,
    stateSave: false,
    pageSize: 10,
    columns: {
      ref_code: { title: "Code" },
      ref_name: { title: "Name" },
      ref_desc: { title: "Description" },
      action: {
        title: "Actions",
        render: (item: any, data: any) => {
          if (!data || typeof data.id === 'undefined') {
            return `<div class="flex justify-center gap-2">No actions available</div>`;
          }

          const isDeleted = data.deleted_at !== null;
          let menuAksi = `<div class="flex justify-center gap-2">`;

          if (isDeleted) {
            menuAksi += `
              <button class="btn btn-sm btn-icon btn-light-success btn-clear" title="Restore"
                      data-action="restore-detail"
                      data-id="${data.id}"
                      data-code="${data.ref_code || ''}"
                      data-name="${data.ref_name || ''}">
                <i class="ki-outline ki-arrow-up text-success"></i>
              </button>`;
            menuAksi += `
              <button class="btn btn-sm btn-icon btn-light-danger btn-clear" title="Permanently Delete"
                      data-action="force-delete-detail"
                      data-id="${data.id}"
                      data-code="${data.ref_code || ''}"
                      data-name="${data.ref_name || ''}">
                <i class="ki-solid ki-trash-square text-danger"></i>
              </button>`;
          } else {
            menuAksi += `
              <button class="btn btn-sm btn-icon btn-light-primary btn-clear" title="Edit"
                      data-action="edit-detail"
                      data-id="${data.id}"
                      data-code="${data.ref_code || ''}"
                      data-name="${data.ref_name || ''}"
                      data-desc="${data.ref_desc || ''}">
                <i class="ki-outline ki-pencil text-primary"></i>
              </button>`;
            menuAksi += `
              <button class="btn btn-sm btn-icon btn-light-danger btn-clear" title="Delete"
                      data-action="delete-detail"
                      data-id="${data.id}"
                      data-code="${data.ref_code || ''}"
                      data-name="${data.ref_name || ''}">
                <i class="ki-outline ki-trash text-danger"></i>
              </button>`;
          }
          menuAksi += `</div>`;
          return menuAksi;
        },
      },
    },
  });
  element.removeEventListener("click", handleDetailTableAction);
  element.addEventListener("click", handleDetailTableAction);
}
}

onMounted(() => {
const initOrReloadDatatable = () => {
  if (parentKey.value) {
      datatable = null;
      initializeDatatable();
  } else {
      console.warn("Cannot initialize datatable: parentKey is not available.");
  }
};

watch(parentKey, (newKey) => {
  if (newKey) {
    nextTick().then(initOrReloadDatatable);
  }
}, { immediate: true });

watch(showTrashedDetails, () => {
  nextTick().then(initOrReloadDatatable);
});

if (!parentKey.value) {
  const unwatchRouter = router.on('success', () => {
      if (parentKey.value) {
          nextTick().then(initOrReloadDatatable);
          if(typeof unwatchRouter === 'function') unwatchRouter();
      }
  });
  setTimeout(() => {
      if (parentKey.value && !datatable) {
           nextTick().then(initOrReloadDatatable);
           if(typeof unwatchRouter === 'function') unwatchRouter();
      }
  }, 700);
}
});
</script>

<template>
<Head :title="title" />
<AppLayout
  :data_page="{ title: title || 'Detail' }"
  :data_breadcrumb="[
    { title: 'Dashboard', url: route('dashboard') },
    { title: 'Reference Data', url: route('reference-data.index') },
    { title: title || 'Detail', url: null }
  ]"
>
  <div class="mx-auto space-y-6">
    <Toast ref="toastRef" />

    <!-- Flash Messages -->
    <div v-if="flashMessage" class="alert alert-success">
      {{ flashMessage }}
    </div>
    <div v-if="flashError" class="alert alert-danger">
      {{ flashError }}
    </div>
    
    <!-- Page Header with Buttons and Toggle -->
    <div class="flex items-center justify-between mb-6">
      <div class="form-check form-switch form-check-custom form-check-solid">
        <!-- <input 
          class="form-check-input" 
          type="checkbox" 
          v-model="showTrashedDetails" 
          id="showTrashedDetailsToggle"
        />
        <label class="form-check-label" for="showTrashedDetailsToggle">
          Tampilkan Data Terhapus
        </label> -->
      </div>
      <div class="flex gap-2">
        <button class="btn btn-primary flex items-center gap-2" @click="openAddDetailModal">
          <i class="ki-outline ki-plus text-lg"></i>
          Tambah Data Detail
        </button>
        <button class="btn btn-success flex items-center gap-2" @click="openImportModal">
          <i class="ki-outline ki-file-up text-lg"></i>
          Import Data
        </button>
      </div>
    </div>

    <ReferenceDataFormModal
      :show="showDetailModal"
      :is-edit-mode="isEditDetailMode"
      :form-object="detailForm"
      item-type="detail"
      :processing="detailForm.processing"
      @submit="submitDetailForm"
      @close="closeDetailModal"
    />
    
    <ReferenceDataImportModal
      v-if="showImportModal"
      :show="showImportModal"
      :parent-key="parentKey"
      @close="closeImportModal"
      @success="handleImportSuccess"
    />
    
    <ConfirmationDialog
      :show="confirmDetailDeleteDialog.show"
      :title="confirmDetailDeleteDialog.title"
      :message="confirmDetailDeleteDialog.message"
      @confirm="confirmDetailDeleteDialog.onConfirm"
      @cancel="closeConfirmDetailDeleteDialog"
    />

    <div class="bg-white p-4 shadow sm:rounded-lg sm:p-8 dark:bg-gray-800">
      <div class="overflow-x-auto">
        <Datatable id="datatable-reference-detail">
          <template #header>
            <tr>
              <th class="min-w-[150px]" data-datatable-column="ref_code">Kode</th>
              <th class="min-w-[150px]" data-datatable-column="ref_name">Nama</th>
              <th class="min-w-[150px]" data-datatable-column="ref_desc">Deskripsi</th>
              <th class="min-w-[100px] text-center" data-datatable-column="action">Aksi</th>
            </tr>
          </template>
        </Datatable>
      </div>
    </div>
  </div>
</AppLayout>
</template>