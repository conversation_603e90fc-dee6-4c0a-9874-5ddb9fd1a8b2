<script setup>
import { ref, onMounted, watch, defineProps, onUnmounted } from "vue";
import Datatable from "@/Components/Partials/Datatable.vue";
import Toast from "@/Components/Partials/Toast.vue";
import axios from "axios";
import dayjs from "dayjs";
import Modal from "@/Components/Partials/Modal.vue";

let datatable = null;
const toastRef = ref(null);
const respondenIdToDelete = ref(null);
const showDeleteModal = ref(false);

const openDeleteModal = () => {
  showDeleteModal.value = true;
};
const closeDeleteModal = () => {
  showDeleteModal.value = false;
  respondenIdToDelete.value = null;
};

const deleteResponden = async () => {
  if (!respondenIdToDelete.value) return;
  
  try {
    await axios.delete(route('responses.destroy', respondenIdToDelete.value));
    datatable.reload();
    toastRef.value?.show('success', 'Berhasil menghapus responden');
  } catch (err) {
    console.error('Error deleting respondent:', err);
    toastRef.value?.show('error', 'Gagal menghapus responden');
  } finally {
    closeDeleteModal();
  }
};

const props = defineProps({
  responses: {
    type: Array,
    default: () => [],
  },
  onTotalChange: {
    type: Function,
    default: null,
  },
});

onMounted(() => {
  const element = document.querySelector("#datatable_responden");
  const dataUrl = route("responses.data") + "?verified_at=0";

  const dataTableOptions = {
    infoEmpty: "Tidak ada data yang tersedia.",
    apiEndpoint: dataUrl,
    stateSave: false,
    pageSize: 10,
    info: "Total: {total}",
    columns: {
      no: {
        title: "NO",
        sortable: false,
        render: (data, row) => row.no ?? "-",
      },
      "respondent.email": {
        title: "EMAIL",
        sortable: true,
        render: (data, row) => {
          let email = row.respondent?.email || "-";
          let submitted = "";
          if (row.submitted_at) {
            let formatted = row.submitted_at;
            if (window.dayjs) {
              formatted = window
                .dayjs(row.submitted_at)
                .format("YYYY-MM-DD HH:mm:ss");
            } else if (typeof dayjs === "function") {
              formatted = dayjs(row.submitted_at).format("YYYY-MM-DD HH:mm:ss");
            } else {
              formatted = row.submitted_at.replace("T", " ").substring(0, 19);
            }
            submitted = `<div class='text-xs text-gray-500'>Submitted: ${formatted}</div>`;
          }
          return `${email}${submitted}`;
        },
      },
      "survey.title": {
        title: "Formulir Survei",
        sortable: true,
        render: (data, row) => row.survey?.title || "-",
      },
      status: {
        title: "STATUS VERIFIKASI",
        sortable: true,
        render: (data, row) => {
          const statusText =
            row.status === 1 ? "Terverifikasi" : "Belum Verifikasi";
          return `
                <span class="px-3 py-1 text-xs font-bold rounded-full ${
                  row.status === 1
                    ? "bg-green-100 text-green-800"
                    : "bg-yellow-100 text-yellow-800"
                }">
                    ${statusText}
                </span>
            `;
        },
      },
      actions: {
        title: "ACTIONS",
        sortable: false,
        render: (data, row) => {
          return `
            <div class="flex items-center gap-3">
              <a href="${route("responses.show", row.id)}" class="flex items-center text-blue-600 inertia-link" target="_blank" rel="noopener">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                Lihat Respon
              </a>
              <button type="button" class="flex items-center text-green-600 btn-verifikasi" data-id="${row.id}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                  <path d="M9 12l2 2l4-4" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Verifikasi
              </button>
              <button type="button" class="flex items-center text-red-600 btn-hapus" data-id="${row.id}" data-modal-toggle="#modal-delete-responden">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                Hapus
              </button>
            </div>
          `;
        },
      },
    },
    onDataLoaded: (result) => {
      if (props.onTotalChange) {
        console.log("onTotalChange dipanggil dengan:", result.totalCount);
        props.onTotalChange(result.totalCount || 0);
      }
    },
  };

  if (datatable && typeof datatable.destroy === "function") {
    datatable.destroy();
  }
  datatable = new KTDataTable(element, {
    ...dataTableOptions,
    apiEndpoint: route("responses.data"),
    requestParams: () => ({ verified_at: 0 }),
  });

  if (datatable && datatable._element) {
    datatable._element.addEventListener("drew", function () {
      const state = datatable.getState ? datatable.getState() : {};
      const total = state.totalItems || 0;
      if (props.onTotalChange) {
        props.onTotalChange(total);
      }
    });
    datatable._element.addEventListener("click", async function (e) {
      const target = e.target.closest(".btn-verifikasi");
      if (target) {
        const id = target.getAttribute("data-id");
        if (id) {
          try {
            await axios.get(route("responses.verify", id));
            datatable.reload();
            toastRef.value?.show("success", "Berhasil verifikasi responden");
          } catch (err) {
            toastRef.value?.show("error", "Gagal verifikasi responden");
          }
        }
      }
      const targetHapus = e.target.closest(".btn-hapus");
      if (targetHapus) {
        respondenIdToDelete.value = targetHapus.getAttribute("data-id");
        openDeleteModal();
      }
    });
  }
});

onUnmounted(() => {
  if (datatable && typeof datatable.destroy === "function") {
    datatable.destroy();
    datatable = null;
  }
});
</script>

<template>
  <Toast ref="toastRef" />
  <Datatable id="datatable_responden" data-name="Responden">
    <template #header>
      <tr>
        <th class="min-w-[50px]" data-datatable-column="no">
          <span class="sort">
            <span class="sort-label">NO</span>
          </span>
        </th>
        <th class="min-w-[150px]" data-datatable-column="respondent.email">
          <span class="sort">
            <span class="sort-label">EMAIL</span>
          </span>
        </th>
        <th class="min-w-[150px]" data-datatable-column="survei.title">
          <span class="sort">
            <span class="sort-label">FORMULIR SURVEI</span>
          </span>
        </th>
        <th class="min-w-[150px]" data-datatable-column="status">
          <span class="sort">
            <span class="sort-label">STATUS VERIFIKASI</span>
          </span>
        </th>
        <th class="min-w-[150px]" data-datatable-column="actions">
          <span class="sort">
            <span class="sort-label">AKSI</span>
          </span>
        </th>
      </tr>
    </template>
  </Datatable>

  <Modal
    id="modal-delete-responden"
    modal-title="Konfirmasi Hapus"
    text-close-button="Batal"
  >
    <p class="text-gray-700">
      Apakah Anda yakin ingin menghapus responden ini?
    </p>
    <template #actions>
      <button
        id="btn-delete-responden"
        data-modal-dismiss="true"
        type="button"
        class="btn btn-danger"
        @click="deleteResponden"
      >
        Hapus
      </button>
    </template>
  </Modal>
</template>
