<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { Head, useForm, usePage, router } from "@inertiajs/vue3";
import { KTDataTable } from "../../../metronic/core/components/datatable/datatable";
import AppLayout from "@/Layouts/AppLayout.vue";
import Datatable from "@/Components/Partials/Datatable.vue";
import Toast from "@/Components/Partials/Toast.vue";
import ConfirmationDialog from "./Partials/ConfirmationDialog.vue";
import ReferenceDataFormModal from "./Partials/ReferenceDataFormModal.vue";

const toastRef = ref();
const page = usePage();
let datatable: any = null;

const showModal = ref(false);
const isEditMode = ref(false);
const editingKey = ref("");
const keyInputRef = ref<HTMLInputElement | null>(null);

const form = useForm({
  key: "",
  title: "",
  description: "",
});

const confirmDialog = ref({
  show: false,
  title: "",
  message: "",
  onConfirm: () => {},
});

const flashMessage = computed(() => page.props.flash?.success || null);
const flashError = computed(() => page.props.flash?.error || null);

function openAddModal() {
  console.log('[openAddModal] Opening modal');
  isEditMode.value = false;
  editingKey.value = "";
  form.reset();
  form.clearErrors();
  showModal.value = true;
  console.log('[openAddModal] showModal.value set to:', showModal.value);
  setTimeout(() => {
    keyInputRef.value?.focus();
    const modalEl = document.getElementById('modal-reference-data');
    console.log('[openAddModal] Modal DOM element:', modalEl);
  }, 50);
}

function openEditModal(data: any) {
  isEditMode.value = true;
  editingKey.value = data.key;
  form.reset();
  form.clearErrors();
  form.title = data.title;
  form.description = data.description;
  showModal.value = true;
}

function validateForm() {
  let isValid = true;
  
  form.clearErrors();
  
  if (!isEditMode.value) {
    if (!form.key) {
      form.setError('key', 'Key Data Referensi wajib diisi.');
      isValid = false;
    } 
  }
  
  if (!form.title) {
    form.setError('title', 'Judul Data Referensi wajib diisi');
    isValid = false;
  } else if (form.title.length < 3) {
    form.setError('title', 'Judul minimal 3 karakter.');
    isValid = false;
  }
  
  return isValid;
}

function submitForm() {
  if (!validateForm()) {
    return;
  }
  
  if (isEditMode.value) {
    form.put(route("reference-data.update", { key: editingKey.value }), {
      onSuccess: () => {
        closeModal();
        if (datatable) datatable.reload();
        toastRef.value?.show("success", "Ubah Data Referensi berhasil.");
      },
      onError: (errors) => {
        if (errors && errors.message === 'Data tidak ditemukan') {
          toastRef.value?.show("error", "Data tidak ditemukan.");
        } else if (errors && Object.keys(errors).length > 0) {
          // Handle validation errors
          const errorMessages = Object.values(errors).flat();
          errorMessages.forEach(message => {
            toastRef.value?.show("error", message);
          });
        } else {
          toastRef.value?.show("error", "Terjadi kesalahan pada sistem.");
        }
        console.error("Error updating reference data:", errors);
      }
    });
  } else {
    form.post(route("reference-data.store"), {
      onSuccess: () => {
        closeModal();
        if (datatable) datatable.reload();
        toastRef.value?.show("success", "Tambah Data Referensi berhasil.");
      },
      onError: (errors) => {
        if (errors && Object.keys(errors).length > 0) {
          // Handle validation errors
          const errorMessages = Object.values(errors).flat();
          errorMessages.forEach(message => {
            toastRef.value?.show("error", message);
          });
        } else {
          toastRef.value?.show("error", "Terjadi kesalahan pada sistem.");
        }
        console.error("Error adding reference data:", errors);
      }
    });
  }
}

function closeModal() {
  showModal.value = false;
  form.reset();
  form.clearErrors();
}

function confirmDelete(key: string, title: string) {
  showConfirmDialog(
    "Konfirmasi Hapus",
    `Apakah Anda yakin ingin menghapus "${title}"?`,
    () => {
      router.delete(route("reference-data.destroy", { key }), {
        onSuccess: () => {
          if (datatable) datatable.reload();
          toastRef.value?.show("success", `"${title}" berhasil dihapus.`);
        },
        onError: (error: any) => {
          if (error?.response?.status === 404) {
            toastRef.value?.show("error", `Data "${title}" tidak ditemukan.`);
          } else {
            toastRef.value?.show("error", `Terjadi kesalahan pada sistem.`);
          }
        },
      });
    }
  );
}

function confirmRestore(key: string, title: string) {
  if (confirm(`Are you sure you want to restore "${title}"?`)) {
    router.patch(route("reference-data.restore", { key }), {}, {
      onSuccess: () => {
        if (datatable) datatable.reload();
        toastRef.value?.show("success", `"${title}" berhasil dipulihkan.`);
      },
      onError: () => {
        toastRef.value?.show("error", `Gagal memulihkan "${title}".`);
      },
    });
  }
}

function confirmForceDelete(key: string, title: string) {
  if (confirm(`Apakah Anda yakin ingin menghapus "${title}" secara permanen? Tindakan ini tidak dapat diurangi!`)) {
    router.delete(route("reference-data.force-delete", { key }), {
      onSuccess: () => {
        if (datatable) datatable.reload();
        toastRef.value?.show("success", `"${title}" berhasil dihapus secara permanen.`);
      },
      onError: (error: any) => {
        if (error?.response?.status === 404) {
          toastRef.value?.show("error", `Data "${title}" tidak ditemukan.`);
        } else {
          toastRef.value?.show("error", `Terjadi kesalahan pada sistem.`);
        }
      },
    });
  }
}

function showConfirmDialog(title: string, message: string, onConfirm: () => void) {
  confirmDialog.value = {
    show: true,
    title,
    message,
    onConfirm: () => {
      onConfirm();
      confirmDialog.value.show = false;
    },
  };
}

function closeConfirmDialog() {
  confirmDialog.value.show = false;
}

function handleTableAction(e: MouseEvent) {
  const target = e.target as Element | null;
  if (!target) return;

  const editBtn = target.closest('[data-action="edit"]');
  const deleteBtn = target.closest('[data-action="delete"]');
  const detailBtn = target.closest('[data-action="detail"]');
  const restoreBtn = target.closest('[data-action="restore"]');
  const forceDeleteBtn = target.closest('[data-action="force-delete"]');

  if (editBtn) {
    const key = editBtn.getAttribute('data-key') || '';
    const title = editBtn.getAttribute('data-title') || '';
    const description = editBtn.getAttribute('data-description') || '';
    openEditModal({ key, title, description });
  } else if (deleteBtn) {
    const key = deleteBtn.getAttribute('data-key') || '';
    const title = deleteBtn.getAttribute('data-title') || '';
    confirmDelete(key, title);
  } else if (restoreBtn) {
    const key = restoreBtn.getAttribute('data-key') || '';
    const title = restoreBtn.getAttribute('data-title') || '';
    confirmRestore(key, title);
  } else if (forceDeleteBtn) {
    const key = forceDeleteBtn.getAttribute('data-key') || '';
    const title = forceDeleteBtn.getAttribute('data-title') || '';
    confirmForceDelete(key, title);
  } else if (detailBtn) {
    const key = detailBtn.getAttribute('data-key') || '';
    router.visit(route("reference-data.detail", { key }));
  }
}

function initializeDatatable(retries = 10) {
  const element = document.querySelector("#datatable-reference-data");

  if (!element && retries > 0) {
    setTimeout(() => initializeDatatable(retries - 1), 300);
    return;
  }

  if (element instanceof HTMLElement) {
    datatable = new KTDataTable(element, {
      infoEmpty: "Tidak ada data yang tersedia.",
      apiEndpoint: route("reference-data.get-data"),
      stateSave: false,
      pageSize: 10,
      columns: {
        key: { title: "Key" },
        title: { title: "Judul" },
        description: { title: "Deskripsi" },
        items: { title: "Jumlah Data" },
        action: {
          title: "Aksi",
          render: (data: any, row: any) => {
            if (!row) return 'No row data';

            const isDeleted = row.deleted_at !== null;

            if (isDeleted) {
              return `
                <div style="display: flex; justify-content: center; gap: 6px;">
                  <button style="padding: 8px; background: #10b981; color: white; border: none; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center;"
                          data-action="restore" data-key="${row.key || ''}" data-title="${row.title || ''}" title="Restore">
                    <i class="ki-outline ki-arrow-up" style="font-size: 14px;"></i>
                  </button>
                  <button style="padding: 8px; background: #dc2626; color: white; border: none; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center;"
                          data-action="force-delete" data-key="${row.key || ''}" data-title="${row.title || ''}" title="Permanently Delete">
                    <i class="ki-outline ki-trash" style="font-size: 14px;"></i>
                  </button>
                </div>
              `;
            } else {
              // Show normal buttons for active items
              let menuAksi = `<div class="flex justify-center gap-2">`;
              menuAksi += `
                <button class="btn btn-sm btn-icon btn-light-primary btn-clear" title="View Details"
                        data-action="detail" data-key="${row.key || ''}">
                  <i class="ki-outline ki-eye text-primary"></i>
                </button>`;
              menuAksi += `
                <button class="btn btn-sm btn-icon btn-light-info btn-clear" title="Edit"
                        data-action="edit" data-key="${row.key || ''}" data-title="${row.title || ''}" data-description="${row.description || ''}">
                  <i class="ki-outline ki-pencil text-info"></i>
                </button>`;
              menuAksi += `
                <button class="btn btn-sm btn-icon btn-light-danger btn-clear" title="Delete"
                        data-action="delete" data-key="${row.key || ''}" data-title="${row.title || ''}">
                  <i class="ki-outline ki-trash text-danger"></i>
                </button>`;
              menuAksi += `</div>`;
              return menuAksi;
            }
          },
        },
      },
    });
    element.removeEventListener("click", handleTableAction);
    element.addEventListener("click", handleTableAction);
  }
}

onMounted(() => {
  setTimeout(() => initializeDatatable(), 500);
});
</script>


<template>
  <Head title="Data Referensi" />
  <AppLayout
    :data_page="{ title: 'Data Referensi' }"
    :data_breadcrumb="[
      { title: 'Dashboard', url: route('dashboard') },
      { title: 'Data Referensi', url: null }
    ]"
  >
    <div class="mx-auto space-y-6">
      <!-- Page Header with Button -->
      <div class="flex items-center justify-end mb-6">
        <button class="btn btn-primary flex items-center gap-2" @click="openAddModal">
          <i class="ki-outline ki-plus text-lg"></i>
          Tambah Data Referensi
        </button>
      </div>
      
      <Toast ref="toastRef" />

      <!-- Flash Messages -->
      <div v-if="flashMessage" class="alert alert-success">
        {{ flashMessage }}
      </div>
      <div v-if="flashError" class="alert alert-danger">
        {{ flashError }}
      </div>
      
      <ReferenceDataFormModal
        :show="showModal"
        :is-edit-mode="isEditMode"
        :form-object="form"
        item-type="parent"
        :processing="form.processing"
        @submit="submitForm"
        @close="closeModal"
      />

      <div class="bg-white p-4 shadow sm:rounded-lg sm:p-8 dark:bg-gray-800">
        <Datatable id="datatable-reference-data">
          <template #header>
            <tr>
              <th class="min-w-[150px]" data-datatable-column="key">Key</th>
              <th class="min-w-[150px]" data-datatable-column="title">Judul</th>
              <th class="min-w-[150px]" data-datatable-column="description">Deskripsi</th>
              <th class="min-w-[100px]" data-datatable-column="items">Jumlah Data</th>
              <th class="min-w-[100px] text-center" data-datatable-column="action">Aksi</th>
            </tr>
          </template>
        </Datatable>
      </div>
    </div>
    <ConfirmationDialog
      :show="confirmDialog.show"
      :title="confirmDialog.title"
      :message="confirmDialog.message"
      @confirm="confirmDialog.onConfirm"
      @cancel="closeConfirmDialog"
    />
  </AppLayout>
</template>


