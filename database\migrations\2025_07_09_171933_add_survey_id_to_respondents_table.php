<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('respondents', function (Blueprint $table) {
            $table->foreignId('survey_id')
                  ->after('id')
                  ->nullable()
                  ->constrained('surveys')
                  ->onDelete('cascade');
        });
    }
    
    public function down(): void
    {
        Schema::table('respondents', function (Blueprint $table) {
            $table->dropForeign(['survey_id']);
            $table->dropColumn('survey_id');
        });
    }
};
