<?php

namespace Database\Factories;

use App\Models\Instrument;
use App\Models\Survey;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstrumentFactory extends Factory
{
    protected $model = Instrument::class;

    private static $level1Counter = 1;
    private static $level2Counter = 1;
    private static $level3Counter = 1;

    public function definition(): array
    {
        return [
            'instrument_name' => $this->faker->sentence(3),
            'instrument_description' => $this->faker->sentence(),
            'instrument_code' => 'L' . (1000 + self::$level1Counter++),
            'instrument_level' => 1,
            'full_instrument_code' => fn (array $attributes) => $attributes['instrument_code'],
            'survey_id' => Survey::factory(),
            'created_by' => 1,
        ];
    }

    public function level2()
    {
        return $this->state(function (array $attributes) {
            $parentCode = $attributes['instrument_code'] ?? 'L1000';
            $code = $parentCode . '.' . (200 + self::$level2Counter++);
            
            return [
                'instrument_code' => $code,
                'instrument_level' => 2,
                'full_instrument_code' => $parentCode . '.' . $code,
            ];
        });
    }

    public function level3()
    {
        return $this->state(function (array $attributes) {
            $parentCode = $attributes['instrument_code'] ?? 'L1000.200';
            $code = $parentCode . '.' . (300 + self::$level3Counter++);
            
            return [
                'instrument_code' => $code,
                'instrument_level' => 3,
                'full_instrument_code' => $attributes['full_instrument_code'] . '.' . $code,
            ];
        });
    }

    public function configure()
    {
        return $this->afterCreating(function (Instrument $instrument) {
            if (!$instrument->created_by) {
                $instrument->update(['created_by' => 1]);
            }
        });
    }
}