<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        $user = $request->user();

        return Inertia::render('Profile/Edit', [
            'user' => [
                'email' => $user->email,
                'role' => $user->role,
                'profile' => $user->profile ? [
                    'name' => $user->profile->name,
                    'phone' => $user->profile->phone_number,
                    'department' => $user->profile->department,
                    'position' => $user->profile->position,
                    'avatar' => $user->profile->avatar,
                ] : null
            ]
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $request->user()->id,
            'phone' => [
                'required',
                'string',
                'min:10',
                'max:20',
                'regex:/^\+?[0-9]+$/',
                'unique:profile,phone_number,' . $request->user()->profile->id
            ],
            'department' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ], [
            'name.required' => 'Nama lengkap wajib diisi',
            'email.required' => 'Email wajib diisi',
            'email.unique' => "Email sudah ada",
            'phone.required' => "Nomor telepon wajib diisi",
            'phone.max' => "Nomor telepon tidak boleh lebih dari 20 karakter",
            'phone.unique' => "Nomor Telepon sudah ada",
            'phone.regex' => "Format nomor telepon tidak sesuai",
            'avatar.max' => "Ukuran gambar terlalu besar (maksimal 2MB)",
            'phone.min' => "Nomor telepon harus valid (minimal 10 digit angka)",
        ]);

        $user = $request->user();
        $user->email = $validated['email'];
        $user->save();

        $avatarPath = $user->profile->avatar ?? null;

        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
        }
        
        $user->profile()->updateOrCreate(
            ['user_id' => $user->id],
            [
                'name' => $validated['name'],
                'phone_number' => $validated['phone'] ?? null,
                'department' => $validated['department'] ?? null,
                'position' => $validated['position'] ?? null,
                'avatar' => $avatarPath
            ]
        );
        

        return Redirect::route('profile.edit')->with('success', 'Profile updated successfully');
    }
    public function updatePassword(Request $request): RedirectResponse
    {
        $request->validate([
            'current_password' => ['required'],
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/[A-Z]/', 
                'regex:/[a-z]/', 
                'regex:/[0-9]/', 
                'regex:/[!@#$%^&*(),.?":{}|<>]/',
                'confirmed',
            ],
        ], [
            'current_password.required' => 'Password lama wajib diisi.',
            'password.required' => 'Password baru wajib diisi.',
            'password.regex' => 'Kata sandi harus mengandung huruf besar, huruf kecil, angka, dan karakter spesial.',
            'password.min' => 'Password baru minimal 8 karakter.',
            'password.confirmed' => 'Kata sandi tidak sama.',
        ]);

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Password lama salah.']);
        }
        $user->password = Hash::make($request->password);
        $user->save();

        return back()->with('success', 'Password berhasil diubah.');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        // $request->validate([
        //     'password' => ['required', 'current_password'],
        // ]);

        // $user = $request->user();

        // Auth::logout();

        // $user->delete();

        // $request->session()->invalidate();
        // $request->session()->regenerateToken();

        return Redirect::route('login');
    }
}
