<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add deleted_at to surveys table
        Schema::table('surveys', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add deleted_at to instruments table
        Schema::table('instruments', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove deleted_at from surveys table
        Schema::table('surveys', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove deleted_at from instruments table
        Schema::table('instruments', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
