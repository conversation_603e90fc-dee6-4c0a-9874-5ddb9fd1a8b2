<?php

namespace Database\Seeders;

use App\Models\RefData;
use App\Models\RefDataDetail;
use Illuminate\Database\Seeder;

class WilayahSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // refdata
        $refdata_provinsi = RefData::firstOrCreate(
            ['title' => 'Provinsi Wilayah Indonesia'],
            [
                'key'         => 'provinsi-wilayah-indonesia',
                'description' => 'Data provinsi dan kota seluruh Indonesia',
            ]
        )->id;

        $refdata_kabkota = RefData::firstOrCreate(
            ['title' => 'Kabupaten Kota Wilayah Indonesia'],
            [
                'key'         => 'kabkota-wilayah-indonesia',
                'description' => 'Data provinsi dan kota seluruh Indonesia',
            ]
        )->id;

        // refdata_details
        $gzFile = base_path('database/seeders/json_file/refdata_wilayah.json.gz');
        $json = gzdecode(file_get_contents($gzFile));
        $rows = json_decode($json, true);

        $provinceCount = 0;
        $cityCount = 0;

        foreach ($rows as $key => $value) {
            if (RefDataDetail::where('ref_code', $value['ref_code'])->exists()) {
                continue;
            }

            $data = [
                'refdata_type' => $value['refdata_type'],
                'ref_code'     => $value['ref_code'],
                'ref_name'     => $value['ref_name'],
                'ref_desc'     => $value['ref_desc'] ?? null,
                'refdata_id'   => $refdata_provinsi,
            ];

            if ($data['refdata_type'] === 'province') {
                $data['ref_parent_id'] = null;
                $province = RefDataDetail::create($data);
                $provinceCount++;
            } elseif ($data['refdata_type'] === 'city') {
                $data['refdata_id'] = $refdata_kabkota;
                $data['ref_parent_id'] = RefDataDetail::where('ref_code', $value['ref_parent_id'])->firstorfail()->id;

                RefDataDetail::create($data);
                $cityCount++;
            }
        }

        RefData::where('id', $refdata_provinsi)->update(['items' => $provinceCount]);
        RefData::where('id', $refdata_kabkota)->update(['items' => $cityCount]);

        echo "✅ WilayahSeeder completed successfully.\n";
        echo "- Inserted {$provinceCount} provinces and {$cityCount} cities.\n";
    }
}
