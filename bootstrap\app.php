<?php

use App\Http\Middleware\CombinedAuth;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

use Symfony\Component\HttpFoundation\Response;

use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Illuminate\Auth\Access\AuthorizationException;
use Inertia\Inertia;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->alias([
            'auth.combined' => CombinedAuth::class,
        ]);

        $middleware->trustProxies(at: [
            '192.168.0.0/16',
            '172.16.0.0/12',
            '10.0.0.0/8',
            '127.0.0.1/8',
            'fd00::/8',
            '::1'
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->stopIgnoring(AuthorizationException::class);
        $exceptions->render(function (AccessDeniedHttpException $exception) {
            $previous = $exception->getPrevious();
            if ($previous instanceof AuthorizationException) {
                return Inertia::render('Errors/403', [
                    'message' => $previous->getMessage(),
                ]);
            }
        });
    })
    ->create();
