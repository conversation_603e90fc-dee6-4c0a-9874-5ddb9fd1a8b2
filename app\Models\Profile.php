<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    use HasFactory;

    protected $connection = 'mysql';
    protected $table = 'profile';
    
    public $timestamps = false;

    protected $fillable = [
        'name',
        'phone_number',
        'department',
        'position',
        'address',
        'user_id',
        'avatar',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
