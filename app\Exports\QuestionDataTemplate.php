<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class QuestionDataTemplate implements WithTitle, WithHeadings, FromArray, WithEvents
{
	public function title(): string
	{
		return 'List Pertanyaan Import';
	}

	public function headings(): array
	{
		return [
			['TEMPLATE IMPORT DATA PERTANYAAN'],
			[],
			[
				'Pertanyaan',
				'Tipe',
				'Wajib <PERSON>',
				'<PERSON><PERSON><PERSON>ban',
				'',
				'',
				'Pengaturan Linear Scale',
			],
			[
				'',
				'',
				'',
				'Opsi Jawaban Satuan',
				'Opsi Jawaban Baris',
				'Opsi Jawaban Kolom',
				'Label Minimal',
				'Nilai Minimal',
				'Label Maksimal',
				'Nilai Maksimal',
			]
		];
	}
	
	public function array(): array
	{
		$dataContoh = [
			['Contoh Pertanyaan 1', 'Short Answer', 'Ya'],
			['Contoh Pertanyaan 2', 'Paragraph', 'Tidak'],
			['Contoh Pertanyaan 3', 'Dropdown', 'Ya', 'Opsi 1, Opsi 2, Opsi 3'],
			['Contoh Pertanyaan 4', 'Linear Scale', 'Ya', '', '', '', 'Sangat Rendah', '1', 'Sangat Tinggi', '10'],
			['Contoh Pertanyaan 5', 'Multiple Choice Grid', 'Ya', '', 'Opsi Baris 1, Opsi Baris 2', 'Opsi Kolom 1, Opsi Kolom 2, Opsi Kolom 3'],
		];

		return $dataContoh;
	}

	public function registerEvents(): array
	{
		return [
			AfterSheet::class => function (AfterSheet $event) {
				$event->sheet->mergeCells('A1:J1');
				$event->sheet->getStyle('A1')->applyFromArray([
					'font' => [
						'bold' => true,
						'size' => 14,
					],
					'alignment' => [
						'horizontal' => Alignment::HORIZONTAL_CENTER,
					],
				]);

				$event->sheet->mergeCells('A3:A4');
				$event->sheet->mergeCells('B3:B4');
				$event->sheet->mergeCells('C3:C4');
				$event->sheet->mergeCells('D3:F3');
				$event->sheet->mergeCells('G3:J3');
				$event->sheet->getStyle('A3:J4')->applyFromArray([
					'font' => [
						'bold' => true,
						'color' => ['rgb' => 'FFFFFF'],
					],
					'alignment' => [
						'vertical' => Alignment::VERTICAL_TOP,
						'horizontal' => Alignment::HORIZONTAL_CENTER,
					],
					'fill' => [
						'fillType' => Fill::FILL_SOLID,
						'startColor' => ['rgb' => '4472C4'],
					],
					'borders' => [
						'allBorders' => [
							'borderStyle' => Border::BORDER_THIN,
							'color' => ['rgb' => '000000'],
						],
					],
				]);

				$dataRowCount = 10;
				if ($dataRowCount > 0) {
					$event->sheet->getStyle('A4:J' . ($dataRowCount + 3))->applyFromArray([
						'borders' => [
							'allBorders' => [
								'borderStyle' => Border::BORDER_THIN,
								'color' => ['rgb' => '000000'],
							],
						],
					]);
				}

				$event->sheet->getColumnDimension('A')->setWidth(50);
				$event->sheet->getColumnDimension('B')->setWidth(20);
				$event->sheet->getColumnDimension('C')->setWidth(12);
				$event->sheet->getColumnDimension('D')->setWidth(20);
				$event->sheet->getColumnDimension('E')->setWidth(20);
				$event->sheet->getColumnDimension('F')->setWidth(20);
				$event->sheet->getColumnDimension('G')->setWidth(15);
				$event->sheet->getColumnDimension('H')->setWidth(15);
				$event->sheet->getColumnDimension('I')->setWidth(15);
				$event->sheet->getColumnDimension('J')->setWidth(15);

				$instructionRow = $dataRowCount + 6;
				$event->sheet->setCellValue('A' . $instructionRow, 'Petunjuk:');
				$event->sheet->setCellValue('A' . ($instructionRow + 1), '- Jangan mengubah posisi tabel');
				$event->sheet->setCellValue('A' . ($instructionRow + 2), '- Jangan mengubah nama kolom header');
				$event->sheet->setCellValue('A' . ($instructionRow + 3), '- Pastikan format file tetap .xlsx');
				$event->sheet->setCellValue('A' . ($instructionRow + 4), '- Data yang diimpor akan divalidasi sesuai format');
				$event->sheet->setCellValue('A' . ($instructionRow + 4), '- Hapus data contoh sebelum mengimpor data asli');
				$event->sheet->setCellValue('A' . ($instructionRow + 5), '- Tipe pertanyaan bisa dipilih di sheet "List Tipe Pertanyaan" dan harus sama');
				$event->sheet->setCellValue('A' . ($instructionRow + 6), '- Kolom "Wajib Diisi" bisa diisi dengan "Ya" Atau "Tidak"');
				$event->sheet->setCellValue('A' . ($instructionRow + 7), '- Kolom "Opsi Jawaban Satuan" bisa diisi apabila tipe pertanyaan Multiple Choice, Checkbox, atau Dropdown dengan contoh pengisian seperti berikut: Opsi A, Opsi B, Opsi C, dst');
				$event->sheet->setCellValue('A' . ($instructionRow + 8), '- Kolom "Opsi Jawaban Baris" dan "Opsi Jawaban Kolom" bisa diisi apabila tipe pertanyaan Multiple Choice Grid atau Checkbox Grid dengan contoh pengisian seperti berikut: Opsi A, Opsi B, Opsi C, dst');
				$event->sheet->setCellValue('A' . ($instructionRow + 9), '- Kolom "Label Minimal", "Nilai Minimal", "Label Maksimal", dan "Nilai Maksimal" bisa diisi apabila tipe pertanyaan Linear Scale');
			},
		];
	}
}
