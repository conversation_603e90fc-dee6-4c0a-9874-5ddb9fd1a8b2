<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'survey_ids',
    ];

    protected $casts = [
        'survey_ids' => 'array',
    ];

    /**
     * Get the surveys associated with the setting.
     */
    public function surveys()
    {
        return $this->belongsToMany(Survey::class, 'survey_setting', 'setting_id', 'survey_id');
    }
}
