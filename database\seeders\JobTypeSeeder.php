<?php

namespace Database\Seeders;

use App\Models\RefData;
use App\Models\RefDataDetail;
use Illuminate\Database\Seeder;

class JobTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $jobType = [
            '<PERSON>',
            '<PERSON><PERSON>',
            'Tenaga Pendidik',
        ];

        $refdata_jobtype = RefData::firstOrCreate(
            ['title' => '<PERSON><PERSON>'],
            [
                'key'         => 'respondent-job-type',
                'description' => '<PERSON><PERSON>ek<PERSON>',
            ]
        )->id;

        $jobTypeCount = 0;
        foreach ($jobType as $key => $value) {
            if (!RefDataDetail::where('ref_code', $value)->exists()) {
                $data = [
                    'refdata_type' => 'respondent-job',
                    'ref_code'     => $value,
                    'ref_name'     => $value,
                    'ref_desc'     => null,
                    'refdata_id'   => $refdata_jobtype,
                ];

                RefDataDetail::create($data);
                $jobTypeCount++;
            }
        }

        RefData::where('id', $refdata_jobtype)->update(['items' => $jobTypeCount]);

        echo "✅ JobTypeSeeder completed successfully.\n";
        echo "- Inserted {$jobTypeCount} Job Types.\n";
    }
}
