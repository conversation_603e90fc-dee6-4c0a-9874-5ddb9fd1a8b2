<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSPK;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Rules\Password as PasswordRules;

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Schema;
use Inertia\Inertia;
use Inertia\Response;

class ForgotPasswordController extends Controller
{
    public function index(): Response
    {
        return Inertia::render('Auth/ForgotPassword');
    }

    public function request(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'email' => 'required|email',
        ]);

        $broker = null;
        if (User::where('email', $request->email)->exists()) {
            $broker = 'users';
        } else {
            try {
                if (UserSPK::where('email', $request->email)->exists()) {
                    $broker = 'spk_users';
                }
            } catch (\Throwable $e) {
                \Log::warning("SPK database unavailable: " . $e->getMessage());
            }
        }

        $status = Password::broker($broker)->sendResetLink(
            $request->only('email')
        );

        if ($status == Password::RESET_LINK_SENT) {
            return back()->with([
                'from' => 'send_password_reset_link',
                'status' => __($status),
            ]);
        }

        throw ValidationException::withMessages([
            'email' => [trans($status)],
        ]);
    }

    public function create(Request $request)
    {
        $user = User::where('email', $request->email)->first();
        $broker = 'users';
        if (!$user) {
            $user = UserSPK::where('email', $request->email)->first();
            $broker = 'spk_users';
        }

        if (empty($user)) {
            return redirect()->route('login')->with([
                'from' => 'password_reset',
                'status' => 'Terjadi Kesalahan',
                'message' => 'Pastikan link reset password Anda masih aktif.',
            ]);
        }

        $isTokenValid = Password::tokenExists($user, $request->route('token'));
        if (!$isTokenValid) {
            return redirect()->route('login')->with([
                'from' => 'password_reset',
                'status' => 'Terjadi Kesalahan',
                'message' => 'Pastikan link reset password Anda masih aktif.',
            ]);
        }

        return Inertia::render('Auth/ResetPassword', [
            'email' => $request->email,
            'token' => $request->route('token'),
        ]);
    }

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => ['required', 'confirmed', PasswordRules::defaults()],
        ]);

        $broker = null;
        if (User::where('email', $request->email)->first()) {
            $broker = 'users';
        } elseif (UserSPK::where('email', $request->email)->first()) {
            $broker = 'spk_users';
        }

        // Here we will attempt to reset the user's password. If it is successful we
        // will update the password on an actual user model and persist it to the
        // database. Otherwise we will parse the error and return the response.
        $status = Password::broker($broker)->reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user) use ($request) {
                $user->password = Hash::make($request->password);

                // Only fill remember_token if the column exists
                if (Schema::hasColumn($user->getTable(), 'remember_token')) {
                    $user->remember_token = Str::random(60);
                }

                $user->save();

                event(new PasswordReset($user));
            }
        );

        // If the password was successfully reset, we will redirect the user back to
        // the application's home authenticated view. If there is an error we can
        // redirect them back to where they came from with their error message.
        if ($status == Password::PASSWORD_RESET) {
            return redirect()->route('login')->with([
                'from' => 'password_reset',
                'status' => __($status),
            ]);
        }

        throw ValidationException::withMessages([
            'email' => [trans($status)],
        ]);
    }
}
