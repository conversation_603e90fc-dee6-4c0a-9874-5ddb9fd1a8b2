<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Survey extends Model
{
    use HasFactory, SoftDeletes;
    
    protected $fillable = [
        'code',
        'title',
        'description',
        'status',
        'link',
        'uuid',
        'created_by',
        'start_date',
        'end_date',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];
    
    /**
     * Get the route key name for <PERSON><PERSON>'s route model binding.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
    if (request()->routeIs('survey.public') || request()->routeIs('formulir-survei.respondents.store')) {
            return 'uuid';
        }
        
        return 'id';
    }

    public function instruments(): HasMany
    {
        return $this->hasMany(Instrument::class)
                   ->whereNull('parent_id')
                   ->with(['children.questions', 'questions']);
    }

    /**
     * Get all questions through instruments
     * This will only return non-deleted questions by default
     */
    public function questions(): \Illuminate\Database\Eloquent\Relations\HasManyThrough
    {
        return $this->hasManyThrough(
            Question::class,
            Instrument::class,
            'survey_id',
            'instrument_id',
            'id',
            'id'
        );
    }

    /**
     * Get all questions including soft deleted ones
     */
    public function questionsWithTrashed()
    {
        return $this->hasManyThrough(
            Question::class,
            Instrument::class,
            'survey_id',
            'instrument_id',
            'id',
            'id'
        )->withTrashed();
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function questionInstruments(): HasOneThrough
    {
        return $this->hasOneThrough(
            Instrument::class,
            Question::class,
            'survey_id',
            'question_id',
            'id',
            'id'
        );
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::deleting(function ($survey) {
            if ($survey->isForceDeleting()) {
                $survey->instruments()->withTrashed()->each(function ($instrument) {
                    $instrument->forceDelete();
                });
            } else {
                $survey->instruments->each->delete();
            }
        });
        
        static::restoring(function ($survey) {
            $survey->instruments()->withTrashed()->get()->each->restore();
        });
    }
    
    /**
     * Get all of the instruments, including trashed ones.
     */
    public function instrumentsWithTrashed()
    {
        return $this->hasMany(Instrument::class, 'survey_id')->withTrashed();
    }
    
    /**
     * Get all responses for the survey
     */
    public function responses()
    {
        return $this->hasMany(Response::class, 'survey_id');
    }
    
    /**
     * Check if the survey is currently active based on date range
     *
     * @return bool
     */
    public function isActiveByDate()
    {
        $now = now();
        
        if (is_null($this->start_date) && is_null($this->end_date)) {
            return true;
        }
        
        $afterStart = $this->start_date ? $now->gte($this->start_date) : true;

        $beforeEnd = $this->end_date ? $now->lte($this->end_date) : true;
        
        return $afterStart && $beforeEnd;
    }
    
    /**
     * Get all respondents for the survey through responses
     */
    public function respondents()
    {
        return $this->hasManyThrough(
            Respondent::class,
            Response::class,
            'survey_id', // Foreign key on responses table
            'id',        // Foreign key on respondents table
            'id',        // Local key on surveys table
            'respondent_id' // Local key on responses table
        );
    }
}