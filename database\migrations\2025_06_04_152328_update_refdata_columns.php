<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateRefdataColumns extends Migration
{
    public function up()
    {
        Schema::table('refdata', function (Blueprint $table) {
            $table->dropColumn(['desc', 'refdata_type']);

            $table->string('key')->nullable();
            $table->text('description')->nullable();
            $table->integer('items')->nullable();
        });
    }

    public function down()
    {
        Schema::table('refdata', function (Blueprint $table) {
            $table->text('desc')->nullable();
            $table->string('refdata_type')->nullable();

            $table->dropColumn(['key', 'description', 'items']);
        });
    }
}

