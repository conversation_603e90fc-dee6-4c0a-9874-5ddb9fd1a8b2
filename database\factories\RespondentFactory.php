<?php

namespace Database\Factories;

use App\Models\Respondent;
use Illuminate\Database\Eloquent\Factories\Factory;

class RespondentFactory extends Factory
{
    protected $model = Respondent::class;

    public function definition()
    {
        return [
            'email' => $this->faker->unique()->safeEmail(),
            'born_date' => $this->faker->date(),
            'job_type' => $this->faker->jobTitle,
            'is_member' => false,
            'created_at' => now()
        ];
    }
}
