<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class QuestionTypesSheet implements WithTitle, WithHeadings, FromArray, ShouldAutoSize, WithEvents
{
	public function title(): string
	{
		return 'List Tipe Pertanyaan';
	}

	public function headings(): array
	{
		return [
			['DATA TIPE PERTANYAAN TERSEDIA'],
			[],
			['Tipe Pertanyaan'],
		];
	}

	public function array(): array
	{
		$data = [
			['Short Answer'],
			['Paragraph'],
			['Multiple Choice'],
			['Checkbox'],
			['Dropdown'],
			['Linear Scale'],
			['Multiple Choice Grid'],
			['Checkbox Grid'],
			['Date'],
			['Time'],
		];

		return $data;
	}

	public function registerEvents(): array
	{
		return [
			AfterSheet::class => function (AfterSheet $event) {
				$event->sheet->getStyle('A1')->applyFromArray([
					'font' => [
						'bold' => true,
						'size' => 14,
					],
					'alignment' => [
						'horizontal' => Alignment::HORIZONTAL_CENTER,
					],
				]);

				$event->sheet->getStyle('A3')->applyFromArray([
					'font' => [
						'bold' => true,
						'color' => ['rgb' => 'FFFFFF'],
					],
					'fill' => [
						'fillType' => Fill::FILL_SOLID,
						'startColor' => ['rgb' => '4472C4'],
					],
					'borders' => [
						'allBorders' => [
							'borderStyle' => Border::BORDER_THIN,
							'color' => ['rgb' => '000000'],
						],
					],
				]);

				$dataRowCount = count($this->array());
				if ($dataRowCount > 0) {
					$event->sheet->getStyle('A4:A' . ($dataRowCount + 3))->applyFromArray([
						'borders' => [
							'allBorders' => [
								'borderStyle' => Border::BORDER_THIN,
								'color' => ['rgb' => '000000'],
							],
						],
					]);
				}
			},
		];
	}
}
