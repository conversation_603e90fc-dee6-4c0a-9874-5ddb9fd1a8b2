<script setup lang="ts">
import Modal from '@/Components/Partials/Modal.vue';
import axios from 'axios';
import { ref } from 'vue';

const isDragging = ref(false);
const selectedFile = ref(null as File | null);
const fileError = ref("");
const fileInput = ref(null as HTMLInputElement | null);

const handleFileDrop = async (event: DragEvent) => {
  isDragging.value = false;
  const files = event.dataTransfer?.files;

  if (files && files.length > 0) {
    validationFile(files);
  }
};

const handleFileSelect = async (event: any) => {
  const files = event.target.files;

  if (files && files.length > 0) {
    validationFile(files);
  }
};

const validationFile = (files: FileList) => {
  if (files && files.length > 0) {
    const file = files[0];
    const validTypes = [
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/csv",
    ];
    const validExt = [".csv", ".xls", ".xlsx"];

    if (
      validTypes.includes(file.type) ||
      validExt.some((ext) => file.name.toLowerCase().endsWith(ext))
    ) {
      selectedFile.value = file;
      fileError.value = "";
    } else {
      fileError.value = "Hanya file Excel (.xls, .xlsx) atau CSV (.csv) yang diizinkan";
      selectedFile.value = null;
    }
  }
}

const submitToServer = async () => {
  if (!selectedFile.value) return;
  const formData = new FormData();
  formData.append("file", selectedFile.value);

  const submitButton = document.querySelector('#submit-button');

  if (submitButton) {
    (submitButton as HTMLButtonElement).disabled = true;
    submitButton.innerHTML = "Memproses File...";
  }

  try {
    const response = await axios.post(
      route("bank-soal.import"),
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          Accept: "application/json",
        },
      }
    );

    if (submitButton) {
      (submitButton as HTMLButtonElement).disabled = false;
      submitButton.innerHTML = "Import Pertanyaan";
    }

    const responseData = response.data.data || response.data;

    if (responseData.errors && responseData.errors.length > 0) {
      emit('error-import', responseData.errors[0].message);
    } else {
      emit('success-import');

      const modal = document.querySelector('#modal-import-question');
  
      if (modal) {
        const modalButtonClose = modal.querySelector('[data-modal-dismiss]');

        if (modalButtonClose) {
          (modalButtonClose as HTMLButtonElement).click();
        }
      }
    }
  } catch (error) {
    console.error("Import error:", error);
  }
};

const emit = defineEmits<{
  (e: 'success-import'): void;
  (e: 'error-import', msg: string): void;
}>();

defineExpose({
  resetForm: () => {
    selectedFile.value = null;
    fileError.value = "";
  }
});
</script>

<template>
  <Modal
    id="modal-import-question"
    modal-title="Import Pertanyaan"
    text-close-button="Batal"
    :maxWidth="'max-w-xl'"
  >
    <div class="space-y-6">
      <div
        class="border-2 border-dashed border-gray-300 rounded-lg px-6 py-10 text-center transition-colors relative cursor-pointer"
        :class="{ 'border-blue-400 bg-blue-50': isDragging }"
        @dragover.prevent="isDragging = true"
        @dragleave="isDragging = false"
        @drop.prevent="handleFileDrop"
        @click="fileInput?.click()"
      >
        <div class="flex flex-col items-center justify-center space-y-2">
          <i class="ki-outline ki-upload text-4xl text-blue-400 mb-2"></i>
          <span
            class="block text-blue-700 cursor-pointer font-medium text-base"
            @click.stop="fileInput?.click()"
          >
            Klik untuk mengunggah
          </span>
          <span class="block text-gray-500 text-sm">atau drag and drop</span>
          <span class="block text-xs text-gray-400"
            >Format file: .xlsx (maks. 10MB)</span
          >
        </div>
        <input
          id="fileInput"
          type="file"
          ref="fileInput"
          class="hidden"
          accept=".csv,.xls,.xlsx"
          @change="handleFileSelect"
        />
        <div v-if="selectedFile" class="mt-4 text-sm text-gray-700">
          File terpilih:
          <span class="font-semibold">{{ selectedFile.name }}</span>
        </div>
        <div v-if="fileError" class="mt-2 text-sm text-red-600">
          {{ fileError }}
        </div>
      </div>
      <div
        class="bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-between"
      >
        <div class="flex items-center space-x-3">
          <i class="ki-outline ki-document text-2xl text-blue-500"></i>
          <div>
            <div class="font-medium text-gray-700">Download template</div>
            <div class="text-xs text-gray-500">
              Dapatkan file contoh untuk mengisi data Anda. File ini sudah
              berisi urutan kolom dan format yang benar.
            </div>
          </div>
        </div>
        <a
          class="btn btn-light flex items-center space-x-2"
          target="_blank"
          :href="route('bank-soal.download-template')"
        >
          <i class="ki-filled ki-file-down"></i>
          <span> Download Template </span>
        </a>
      </div>
    </div>

    <template #actions>
      <button
        id="submit-button"
        type="button"
        @click="submitToServer()"
        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md btn-primary disabled:opacity-50 disabled:cursor-wait disabled:hover:shadow-none"
      >
        Import File
      </button>
    </template>
  </Modal>
</template>
