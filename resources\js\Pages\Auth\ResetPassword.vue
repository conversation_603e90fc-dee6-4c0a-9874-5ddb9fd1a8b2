<script setup lang="ts">
import FullLayout from "@/Layouts/FullLayout.vue";
import InputError from "@/Components/Partials/InputError.vue";
import InputLabel from "@/Components/Partials/InputLabel.vue";
import TextInput from "@/Components/Partials/TextInput.vue";
import TextInputPassword from "@/Components/Partials/TextInputPassword.vue";
import Button from "@/Components/Partials/Button.vue";
import { Head, Link, useForm } from "@inertiajs/vue3";
import { computed, ref, watch, onMounted } from "vue";

const props = defineProps<{
  email: string;
  token: string;
}>();

const form = useForm({
  token: props.token,
  email: props.email,
  password: "",
  password_confirmation: "",
});

const passwordErrors = ref<string>();
const confirmPasswordErrors = ref<string>();

const isFormValid = computed(() => {
  return (
    !passwordErrors.value &&
    !confirmPasswordErrors.value &&
    form.password &&
    form.password_confirmation &&
    form.password === form.password_confirmation &&
    hasUpperCase(form.password) &&
    hasLowerCase(form.password) &&
    hasNumber(form.password) &&
    hasMinLength(form.password) &&
    hasSpecialChar(form.password)
  );
});

const hasUpperCase = (password: string) => /[A-Z]/.test(password);
const hasLowerCase = (password: string) => /[a-z]/.test(password);
const hasNumber = (password: string) => /[0-9]/.test(password);
const hasMinLength = (password: string) => password.length >= 8;
const hasSpecialChar = (password: string) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

const getPasswordValidationErrors = (password: string): string[] => {
  const errors: string[] = [];

  if (!hasMinLength(password)) {
    errors.push("Kata sandi harus minimal 8 karakter.");
  }
  if (!hasUpperCase(password)) {
    errors.push("Kata sandi harus mengandung huruf besar (A-Z).");
  }
  if (!hasLowerCase(password)) {
    errors.push("Kata sandi harus mengandung huruf kecil (a-z).");
  }
  if (!hasNumber(password)) {
    errors.push("Kata sandi harus mengandung angka (0-9).");
  }
  if (!hasSpecialChar(password)) {
    errors.push("Kata sandi harus mengandung karakter spesial (contoh: !@#$%^).");
  }

  return errors;
};

const validatePassword = (password: string) => {
  if (!password) {
    passwordErrors.value = "Kata sandi tidak boleh kosong.";
  } else {
    passwordErrors.value = getPasswordValidationErrors(password).join("<br>");
  }
};

const validateConfirmPassword = (password_confirmation: string) => {
  if (!password_confirmation) {
    confirmPasswordErrors.value = "Konfirmasi kata sandi baru wajib diisi.";
  } else if (form.password !== password_confirmation) {
    confirmPasswordErrors.value = "Kata sandi tidak sama.";
  } else {
    const errors = getPasswordValidationErrors(password_confirmation);
    confirmPasswordErrors.value = errors.length ? errors.join("<br>") : "";
  }
};

watch(
  () => form.password,
  () => {
    validatePassword(form.password);
    validateConfirmPassword(form.password_confirmation);
  }
);

watch(
  () => form.password_confirmation,
  () => {
    validateConfirmPassword(form.password_confirmation);
  }
);

// onMounted(() => {
//   validatePassword(form.password);
//   validateConfirmPassword(form.password_confirmation);
// });

const submit = () => {
  validatePassword(form.password);
  validateConfirmPassword(form.password_confirmation);

  if (passwordErrors.value || confirmPasswordErrors.value) {
    return;
  }

  form.post(route("password.store"), {
    onFinish: () => {
      form.reset("password", "password_confirmation");
    },
  });
};
</script>

<template>
  <FullLayout>
    <Head title="Atur Ulang Kata Sandi" />

    <div class="flex justify-center items-center py-8 lg:px-10 h-full w-full">
      <div class="card max-w-[550px] m-10">
        <div class="card-header border-0 pt-5 gap-5">
          <div>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-12 w-12 text-blue-600">
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
              <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
              <path d="M10 9H8"></path>
              <path d="M16 13H8"></path>
              <path d="M16 17H8"></path>
            </svg>
          </div>

          <div class="text-center w-full">
            <h1 class="text-2xl font-bold text-center text-gray-800 mb-2.5">Reset Kata Sandi</h1>
            <div class="flex items-center justify-center font-normal">
              <span class="text-2sm text-gray-700 me-1.5"> Silahkan buat kata sandi baru dibawah. Diharapkan tidak menyebarkan kata sandi baru Anda ke orang lain. </span>
            </div>
          </div>
        </div>

        <form class="card-body flex flex-col gap-5" @submit.prevent="submit">
          <TextInput id="email" type="hidden" v-model="form.email" required />

          <div>
            <InputLabel for="password" value="Kata Sandi Baru" />
            <TextInputPassword id="password" class="mt-1" v-model="form.password" required autocomplete="new-password" placeholder="Masukan kata sandi baru" :class="{ 'border-red-500': passwordErrors }" />

            <InputError class="mt-2" :message="passwordErrors || form.errors.password" />
          </div>

          <div>
            <InputLabel for="password_confirmation" value="Konfirmasi Kata Sandi Baru" />
            <TextInputPassword
              id="password_confirmation"
              class="mt-1"
              v-model="form.password_confirmation"
              required
              autocomplete="new-password"
              placeholder="Konfirmasi kata sandi baru"
              :class="{ 'border-red-500': form.password_confirmation && form.password !== form.password_confirmation }"
            />

            <InputError class="mt-2" :message="confirmPasswordErrors || form.errors.password_confirmation" />
          </div>

          <div class="flex items-center justify-end mt-4">
            <Button id="button_reset_password" class="grow" :class="{ 'opacity-25': form.processing }" :disabled="form.processing || !isFormValid"> Simpan Kata Sandi Baru </Button>
          </div>
        </form>
      </div>
    </div>
  </FullLayout>
</template>
