<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Auth\Passwords\PasswordResetServiceProvider;
use Illuminate\Auth\Passwords\DatabaseTokenRepository;
use Illuminate\Support\Facades\Gate;
use Str;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->app->bind('auth.password.tokens.spk_users', function ($app) {
            $config = $app['config']['auth.passwords.spk_users'];
            return new DatabaseTokenRepository(
                $app['db']->connection('mysql'),
                $app['hash'],
                $config['table'],
                $config['expire'],
                $config['throttle'] ?? 0
            );
        });

        Gate::define(
            'superadmin',
            fn($user) =>
            Str::lower($user->role_name) === 'superadmin'
        );

        Gate::define(
            'pengelola',
            fn($user) =>
            Str::lower($user->role_name) === 'pengelola'
        );

        Gate::define('responden_anggota', fn($user) => in_array(
            Str::lower($user->role_name),
            ['responden_anggota', 'administrator', 'ketua', 'pengurus', 'bendahara', 'anggota']
        ));
    }
}
