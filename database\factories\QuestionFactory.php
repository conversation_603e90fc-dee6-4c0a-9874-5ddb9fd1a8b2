<?php

namespace Database\Factories;

use App\Models\Question;
use Illuminate\Database\Eloquent\Factories\Factory;

class QuestionFactory extends Factory
{
    protected $model = Question::class;

    public function definition()
    {
        return [
            'survey_id' => \App\Models\Survey::factory(),
            'instrument_id' => \App\Models\Instrument::factory(),
            'question_text' => $this->faker->sentence() . '?',
            'question_type' => 'text',
            'options' => null,
            'q_answer' => null,
            'required' => $this->faker->boolean(),
            'position' => $this->faker->numberBetween(1, 100),
            'created_at' => now()
        ];
    }
}
