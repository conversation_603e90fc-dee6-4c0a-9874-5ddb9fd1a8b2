<?php

namespace App\Http\Controllers;

use App\Models\Respondent;
use App\Models\Response;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;


class RespondentController extends Controller
{
    public function index()
    {
        return Inertia::render('Responden/IndexResponden');
    }

    public function getData(Request $request)
    {
        try {
            $page = (int) ($request->input('page') ?? 1);
            $perPage = (int) ($request->input('size') ?? 10);
            $search = $request->input('search');
            $verifiedAt = $request->input('verified_at');

            $query = Response::with('respondent:id,email,is_member', 'survey:id,title');
            $query->whereNotNull('submitted_at');
            if ($verifiedAt === "1") {
                $query->whereNotNull('verified_at');
            } elseif ($verifiedAt === "0") {
                $query->whereNull('verified_at');
            }
            
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->WhereHas('respondent', function ($sub) use ($search) {
                        $sub->where('email', 'like', "%{$search}%");
                    })
                        ->orWhereHas('survey', function ($sub) use ($search) {
                            $sub->where('title', 'like', "%{$search}%");
                        });
                });
            }

            $data = $query->orderBy('submitted_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $items = $data->items();
            foreach ($items as $i => $item) {
                $item->no = ($page - 1) * $perPage + $i + 1;
            }

            return response()->json([
                'page' => $data->currentPage(),
                'pageCount' => $data->lastPage(),
                'totalCount' => $data->total(),
                'data' => $items,
            ], 200);
        } catch (\Exception $e) {
            \Log::error('Error fetching reference data: ' . $e->getMessage(), ['exception' => $e]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch data: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function formatInstruments($instruments)
    {
        return collect($instruments)->map(function ($instrument) {
            $data = [
                'id' => $instrument->id,
                'name' => $instrument->instrument_name,
                'description' => $instrument->instrument_description,
                'level' => $instrument->instrument_level,
                'code' => $instrument->instrument_code,
                'respondent_type' => $instrument->respondent_type ?? null,
                'questions' => collect($instrument->questions ?? [])->sortBy('position')->map(function ($question) {
                    $options = $question->q_answer ?? null;
                    $columns = null;
                    $rows = null;
                    $scale = null;

                    if (in_array($question->question_type, ['multiple_choice', 'checkbox', 'dropdown'])) {
                        if (is_string($options)) $options = json_decode($options, true);
                        if (is_array($options)) {
                            $options = collect($options)->map(function($opt, $idx) {
                                return ['id' => $idx + 1, 'text' => $opt];
                            })->toArray();
                        }
                    }

                    if (in_array($question->question_type, ['multiple_choice_grid', 'checkbox_grid'])) {
                        $gridSource = is_string($question->q_answer) ? json_decode($question->q_answer, true) : (is_array($question->q_answer) ? $question->q_answer : []);
                        $columns = collect($gridSource['columns'] ?? [])->map(function($col, $idx) {
                            return ['id' => $idx + 1, 'text' => $col];
                        })->toArray();
                        $rows = collect($gridSource['rows'] ?? [])->map(function($row, $idx) {
                            return ['id' => $idx + 1, 'text' => $row];
                        })->toArray();
                    }
                    if ($question->question_type === 'linear_scale') {
                        if (is_string($options)) $options = json_decode($options, true);
                        if (is_array($options)) $scale = $options;
                    }

                    return [
                        'id' => $question->id,
                        'text' => $question->question_text,
                        'type' => $question->question_type,
                        'options' => $options,
                        'columns' => $columns,
                        'rows' => $rows,
                        'scale' => $scale,
                        'required' => (bool) $question->required,
                        'position' => (int) $question->position
                    ];
                })->values(),
                'children' => isset($instrument->children) && count($instrument->children)
                    ? $this->formatInstruments($instrument->children)
                    : []
            ];
            return $data;
        })->sortBy('level')->values();
    }

    public function show(Response $response)
    {
        $response->load([
            'respondent:id,email,job_type',
            'survey.instruments.questions',
            'survey.instruments.children',
            'survey.instruments.children.questions',
            'answer.question'
        ]);

        $formattedInstruments = $this->formatInstruments($response->survey->instruments);

        $responseArr = $response->toArray();
        $respondent = $response->respondent;
        $isMember = false;
        $userData = null;
        if ($respondent) {
            $user = \App\Models\User::with('profile')->where('email', $respondent->email)->first();
            if ($user) {
                $isMember = true;
                $userData = [
                    'id' => $user->id,
                    'name' => $user->profile ? $user->profile->name : $user->name,
                    'email' => $user->email
                ];
            }
        }
        $responseArr['respondent']['is_member'] = $isMember;
        $responseArr['respondent']['user_data'] = $userData;
        $responseArr['survey']['instruments'] = $formattedInstruments;

        if (isset($responseArr['answer']) && is_array($responseArr['answer'])) {
            foreach ($responseArr['answer'] as &$ans) {
                if (isset($ans['question']) && isset($ans['question']['question_type']) && $ans['question']['question_type'] === 'file' && !empty($ans['answer'])) {
                    $ans['answer'] = \Illuminate\Support\Facades\Storage::url($ans['answer']);
                }
            }
            unset($ans);
        }

        return Inertia::render('Responden/ShowResponden', [
            'response' => $responseArr,
        ]);
    }

    public function update(Response $response)
    {
        $response->update(['verified_at' => now()]);

        return response()->json(['message' => 'Response updated successfully.']);
    }

    public function unverify(Response $response)
    {
        $response->update(['verified_at' => null]);
        return response()->json(['message' => 'Response unverified successfully.']);
    }

    public function getCountsResponse()
    {
        $totalMasuk = \App\Models\Response::whereNull('verified_at')->count();
        $totalTerverifikasi = \App\Models\Response::whereNotNull('verified_at')->count();
        return response()->json([
            'masuk' => $totalMasuk,
            'terverifikasi' => $totalTerverifikasi,
        ]);
    }

    public function destroy(Response $response)
    {
        try {
            $answers = $response->answer;
            
            foreach ($answers as $answer) {
                if ($answer->answer && is_string($answer->answer)) {
                    if (str_starts_with($answer->answer, 'answers/')) {
                        if (Storage::exists($answer->answer)) {
                            Storage::delete($answer->answer);
                            \Log::info('Deleted file from storage', ['path' => $answer->answer]);
                        }
                    }
                }
            }
            
            $response->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Response deleted successfully.'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error deleting response: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete response: ' . $e->getMessage()
            ], 500);
        }
    }

    public function downloadAnswerFile(Request $request)
    {
        $path = $request->query('path');
        if (!$path || !Storage::exists($path)) {
            abort(404, 'File tidak ditemukan');
        }
        $filename = basename($path);
        return Storage::download($path, $filename);
    }
}
