<?php

namespace App\Http\Controllers;

use App\Models\User;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Validation\Rule;


class UserManagementController extends Controller
{
    public function index()
    {
        return Inertia::render('UserManagement/IndexUserManagement');
    }

    public function getData(Request $request)
    {
        $query = User::with('profile')->select('id', 'email', 'role', 'is_active', 'created_at');
        $page = $request->input('page') ?? 1;
        $perPage = $request->input('size') ?? 10;
        $search = $request->input('search', null);
        $sortOrder = $request->input('sortOrder') ?? 'desc';
        $sortField = $request->input('sortField') ?? 'created_at';
        $filters = $request->input('filters') ?? [];
        $decodedFilters = is_string($filters) ? json_decode($filters, true) : $filters;
        $startNumber = ($page - 1) * $perPage + 1;

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('email', 'like', '%' . $search . '%')
                    ->orWhereHas('profile', function ($q2) use ($search) {
                        $q2->where('name', 'like', '%' . $search . '%');
                    });
            });
        }

        $roleFilter = collect($decodedFilters)->firstWhere('column', 'role');
        $statusFilter = collect($decodedFilters)->firstWhere('column', 'is_active');
        $role = $roleFilter['value'] ?? strtolower(trim($request->input('role', '')));
        $status = $statusFilter['value'] ?? strtolower(trim($request->input('status', '')));

        if (!empty($role) && $role !== 'semua') {
            $query->where('role', $role);
        }

        if (!empty($status) && $status !== 'semua') {
            $query->where('is_active', $status === 'aktif' ? 1 : 0);
        }

        $totalCount = $query->count();

        $users = $query
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->profile->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'status' => (int)$user->is_active,
                ];
            });

        return response()->json([
            'page' => $page,
            'pageCount' => 1,
            'sortField' => $sortField,
            'sortOrder' => $sortOrder,
            'totalCount' => $totalCount,
            'data' => $users,
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users', 'email')->whereNull('deleted_at'),
            ],
            'role' => 'required|string|max:255',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/[A-Z]/',
                'regex:/[a-z]/',
                'regex:/[0-9]/',
                'regex:/[!@#$%^&*(),.?":{}|<>]/',
                'confirmed',
            ],
            'status' => 'string|max:255',
            'telp' => [
                'required',
                'string',
                'regex:/^\d{10,}$/',
            ],
            'lembaga' => 'nullable|string',
            'jabatan' => 'nullable|string',
            'alamat' => 'nullable|string',
        ], [
            'name.required' => 'Nama lengkap wajib diisi',
            'email.required' => 'Email wajib diisi',
            'email.unique' => 'Email sudah ada',
            'telp.required' => "Nomor telepon wajib diisi",
            'telp.max' => "Nomor telepon tidak boleh lebih dari 20 karakter",
            'telp.unique' => "Nomor Telepon sudah ada",
            'telp.regex' => "Format nomor telepon tidak sesuai",
            'telp.min' => "Nomor telepon harus valid (minimal 10 digit angka)",
            'password.required' => 'Password baru wajib diisi.',
            'password.regex' => 'Kata sandi harus mengandung huruf besar, huruf kecil, angka, dan karakter spesial.',
            'password.min' => 'Password baru minimal 8 karakter.',
            'password.confirmed' => 'Kata sandi tidak sama.',
        ]);
    
        $validator->after(function ($validator) use ($request) {
            $exists = \App\Models\Profile::where('phone_number', $request->telp)
                ->whereHas('user', function ($q) {
                    $q->whereNull('deleted_at');
                })
                ->first();
            if ($exists) {
                $validator->errors()->add('telp', 'Nomor Telepon sudah ada');
            }
        });
    
        if ($validator->fails()) {
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'message' => 'Validasi gagal',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }
    
        $user = User::create([
            'email' => $validator->validated()['email'],
            'password' => bcrypt($validator->validated()['password']),
            'role' => $validator->validated()['role'],
            'email_verified_at' => now(),
            'remember_token' => Str::random(10),
        ]);
    
        $profileData = [
            'user_id' => $user->id,
            'name' => $validator->validated()['name'] ?? null,
            'phone_number' => $validator->validated()['telp'] ?? null,
            'department' => $validator->validated()['lembaga'] ?? null,
            'position' => $validator->validated()['jabatan'] ?? null,
            'address' => $validator->validated()['alamat'] ?? null,
        ];
        \App\Models\Profile::create($profileData);
    
        return response()->json([
            'success' => true,
            'message' => 'Tambah Pengguna Berhasil',
            'data' => [
                'id' => $user->id,
                'email' => $user->email,
                'role' => $user->role,
                'status' => 'Aktif',
            ]
        ]);
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xls,xlsx|max:10240',
        ]);

        $file = $request->file('file');
        $ext = strtolower($file->getClientOriginalExtension());
        $rows = [];

        if (in_array($ext, ['xls', 'xlsx'])) {
            $rows = Excel::toArray([], $file)[0];

            $headerLabels = ['nama', 'email', 'role', 'status'];
            $headerIndex = -1;
            foreach ($rows as $i => $row) {
                $rowLower = array_map(function ($cell) {
                    return strtolower(trim((string) $cell));
                }, $row);
                if (
                    count($rowLower) >= 4 &&
                    $rowLower[0] === $headerLabels[0] &&
                    $rowLower[1] === $headerLabels[1] &&
                    $rowLower[2] === $headerLabels[2] &&
                    $rowLower[3] === $headerLabels[3]
                ) {
                    $headerIndex = $i;
                    break;
                }
            }
            $dataRows = [];
            if ($headerIndex !== -1) {
                for ($i = $headerIndex + 1; $i < count($rows); $i++) {
                    $row = $rows[$i];
                    if (count($row) < 4) continue;
                    $first = strtolower(trim($row[0] ?? ''));
                    if ($first === '' || $first === 'petunjuk:' || str_starts_with($first, '-')) break;
                    $dataRows[] = $row;
                }
            }
        }

        if (count($rows) < 2) {
            return response()->json([
                'success_count' => 0,
                'failure_count' => 0,
                'total' => 0,
                'errors' => [['row' => 1, 'message' => 'File kosong atau tidak ada data', 'data' => []]],
            ]);
        }

        $success = 0;
        $failed = 0;
        $errors = [];

        foreach ($dataRows as $idx => $row) {
            $name = trim($row[0] ?? '');
            $email = trim($row[1] ?? '');
            $role = trim($row[2] ?? 'user');
            $statusRaw = trim($row[3] ?? '');
            $status = (strtolower($statusRaw) === 'aktif' || $statusRaw === '1') ? 1 : 0;

            $validator = Validator::make([
                'name' => $name,
                'email' => $email,
                'role' => $role,
            ], [
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255|unique:users,email',
                'role' => 'required|string|max:255',
            ], [
                'email.email' => 'Format email tidak sesuai',
                'email.required' => 'Email wajib diisi',
                'email.unique' => 'Email sudah ada',
                'name.required' => 'Nama lengkap wajib diisi',
                'role.required' => 'Role wajib diisi',
            ]);

            if ($validator->fails()) {
                $failed++;
                $errors[] = [
                    'row' => $idx + 2,
                    'message' => implode(', ', $validator->errors()->all()),
                    'data' => $row,
                ];
                continue;
            }

            DB::beginTransaction();
            try {
                $user = \App\Models\User::create([
                    'name' => $name,
                    'email' => $email,
                    'password' => bcrypt(Str::random(12)),
                    'role' => $role,
                    'is_active' => $status,
                    'email_verified_at' => now(),
                    'remember_token' => Str::random(10),
                ]);
                \App\Models\Profile::create([
                    'user_id' => $user->id,
                    'name' => $name,
                ]);
                DB::commit();
                $success++;
            } catch (\Throwable $e) {
                DB::rollBack();
                $failed++;
                $errors[] = [
                    'row' => $idx + 2,
                    'message' => 'Gagal import: ' . $e->getMessage(),
                    'data' => $row,
                ];
            }
        }

        return response()->json([
            'success_count' => $success,
            'failure_count' => $failed,
            'total' => count($dataRows),
            'errors' => $errors,
        ]);
    }

    public function edit($id)
    {
        $user = User::with('profile')->findOrFail($id);
        $profile = $user->profile;

        return response()->json([
            'id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'status' => $user->is_active ? 1 : 0,
            'name' => $profile ? $profile->name : null,
            'telp' => $profile ? $profile->phone_number : null,
            'lembaga' => $profile ? $profile->department : null,
            'jabatan' => $profile ? $profile->position : null,
            'alamat' => $profile ? $profile->address : null,
        ]);
    }
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);
    
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($id)->whereNull('deleted_at'),
            ],
            'role' => 'required|string|max:255',
            'status' => 'nullable|integer',
            'telp' => [
                'required',
                'string',
                'regex:/^\+?[0-9]+$/',
            ],
            'lembaga' => 'nullable|string',
            'jabatan' => 'nullable|string',
            'alamat' => 'nullable|string',
        ], [
            'email.unique' => 'Email sudah ada',
            'telp.unique' => 'Nomor Telepon sudah ada',
            'telp.required' => 'Nomor telepon wajib diisi',
            'telp.regex' => 'Format nomor telepon tidak sesuai',
        ]);
    
        $validator->after(function ($validator) use ($request, $id) {
            $exists = \App\Models\Profile::where('phone_number', $request->telp)
                ->where('user_id', '!=', $id)
                ->whereHas('user', function ($q) {
                    $q->whereNull('deleted_at');
                })
                ->first();
            if ($exists) {
                $validator->errors()->add('telp', 'Nomor Telepon sudah ada');
            }
        });
    
        if ($validator->fails()) {
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'message' => 'Validasi gagal',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }
    
        $user->email = $validator->validated()['email'];
        $user->role = $validator->validated()['role'];
        $user->is_active = isset($validator->validated()['status']) ? (int)$validator->validated()['status'] : 0;
        $user->save();
    
        $profile = $user->profile ?? new \App\Models\Profile(['user_id' => $user->id]);
        $profile->name = $validator->validated()['name'] ?? null;
        $profile->phone_number = $validator->validated()['telp'] ?? null;
        $profile->department = $validator->validated()['lembaga'] ?? null;
        $profile->position = $validator->validated()['jabatan'] ?? null;
        $profile->address = $validator->validated()['alamat'] ?? null;
    
        if ($user->profile) {
            $profile->save();
        } else {
            $user->profile()->save($profile);
        }
    
        return response()->json([
            'success' => true,
            'message' => 'Ubah Pengguna Berhasil',
            'data' => [
                'id' => $user->id,
                'name' => $profile->name,
                'email' => $user->email,
                'role' => $user->role,
                'status' => (int)$user->is_active,
                'telp' => $profile->phone_number,
                'lembaga' => $profile->department,
                'jabatan' => $profile->position,
                'alamat' => $profile->address,
            ]
        ]);
    }

    public function updatePassword(Request $request, $id)
    {
        $validated = $request->validate([
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/[A-Z]/',
                'regex:/[a-z]/',
                'regex:/[0-9]/',
                'regex:/[!@#$%^&*(),.?":{}|<>]/',
            ],
            'password_confirmation' => 'required|same:password',
        ], [
            'password.required' => 'Password baru wajib diisi.',
            'password.min' => 'Password baru minimal 8 karakter.',
            'password.regex' => 'Kata sandi harus mengandung huruf besar, huruf kecil, angka, dan karakter spesial.',
            'password.confirmed' => 'Kata sandi tidak sama.',
        ]);

        $user = User::findOrFail($id);
        $user->password = bcrypt($validated['password']);
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Ubah Password Berhasil',
        ]);
    }

    public function updateStatus(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validated = $request->validate([
            'status' => 'required|integer',
        ]);

        $user->is_active = $validated['status'];
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Ubah Status User Berhasil',
            'data' => [
                'id' => $user->id,
                'status' => $user->is_active
            ]
        ]);
    }


    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->is_active = 0;
        $user->save();
        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'Hapus User Berhasil',
        ]);
    }
}
