<?php

namespace App\Http\Middleware;

use App\Models\Survey;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Inertia\Inertia;

class CheckSurveyActive
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $surveyParam = $request->route('survey') ?? $request->route('uuid') ?? $request->route('idOrUuid');
        
        if (!$surveyParam) {
            return $next($request);
        }

        if ($surveyParam instanceof \App\Models\Survey) {
            $survey = $surveyParam;
        } else {
            $survey = \App\Models\Survey::where('uuid', $surveyParam)
                ->orWhere('id', $surveyParam)
                ->first();
        }

        if (!$survey) {
            return $next($request);
        }

        if (!$survey->isActiveByDate()) {
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'message' => 'Masa berlaku survei telah berakhir',
                    'reason' => 'expired',
                    'survey' => [
                        'title' => $survey->title,
                        'start_date' => $survey->start_date?->format('d F Y'),
                        'end_date' => $survey->end_date?->format('d F Y')
                    ]
                ], 403);
            }
            
            if ($request->inertia()) {
                return Inertia::render('Errors/SurveyExpired', [
                    'survey' => [
                        'title' => $survey->title,
                        'start_date' => $survey->start_date?->format('d F Y'),
                        'end_date' => $survey->end_date?->format('d F Y')
                    ]
                ]);
            }
            
            return redirect()->route('survey.expired', ['uuid' => $survey->uuid]);
        }

        return $next($request);
    }
}
