<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class UserSPK extends Authenticatable
{
    use Notifiable;

    protected $connection = 'spk';
    protected $table = 'user';
    protected $fillable = [
        'email',
        'password',
        'role',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    public $timestamps = false;

    public function role()
    {
        return $this->belongsTo(RoleSPK::class, 'role_id', 'id');
    }

    public function getRoleNameAttribute()
    {
        return $this->role->role;
    }
}
