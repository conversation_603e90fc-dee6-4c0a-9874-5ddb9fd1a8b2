<?php

namespace App\Http\Controllers;

use App\Models\Survey;
use App\Models\User;
use App\Models\Respondent;
use App\Models\Response;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    /**
     * Display dashboard with statistics
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $totalSurveys = Survey::count();
            
            $totalResponses = Response::whereNotNull('submitted_at')->count();
            
            $totalRespondents = Respondent::count();

            $unsentResponses = Respondent::whereHas('responses.answer')
                ->whereDoesntHave('responses', function($query) {
                    $query->whereNotNull('submitted_at');
                })
                ->count();

            
            $surveyStatus = [
                'aktif' => Survey::where('status', 'aktif')->count(),
                'nonaktif' => Survey::where('status', 'nonaktif')->count(),
            ];
            
            $verificationStatus = [
                'verified' => Response::whereNotNull('verified_at')->count(),
                'unverified' => Response::whereNotNull('submitted_at')
                    ->whereNull('verified_at')
                    ->count(),
                'total' => $totalResponses
            ];
            
            $latestResponses = Response::with(['survey:id,title', 'respondent'])
                ->whereNotNull('submitted_at')
                ->orderBy('submitted_at', 'desc')
                ->take(3)
                ->get()
                ->map(function($response) {
                    return [
                        'respondent_email' => $response->respondent ? ($response->respondent->email ?? 'No Email') : 'Unknown',
                        'submitted_at' => $response->submitted_at,
                    ];
                });

            $popularSurveys = Survey::withCount(['responses as response_count' => function($query) {
                $query->whereNotNull('submitted_at');
            }])
            ->whereHas('responses')
            ->orderBy('response_count', 'desc')
            ->take(3)
            ->get()
            ->map(function($survey) {
                $totalRespondents = $survey->respondents()->count();
                $completionRate = $totalRespondents > 0 
                    ? round(($survey->response_count / $totalRespondents) * 100) 
                    : 0;
                
                return [
                    'title' => $survey->title,
                    'response_count' => $survey->response_count,
                    'completion_rate' => $completionRate . '%',
                ];
            });
            
            $endDate = now();
            $startDate = now()->copy()->subDays(6)->startOfDay();
            
            $dates = collect();
            $currentDate = $startDate->copy();
            
            while ($currentDate <= $endDate) {
                $dates->push($currentDate->format('Y-m-d'));
                $currentDate->addDay();
            }
            
            $responseTrends = Response::selectRaw('DATE(submitted_at) as date, COUNT(*) as count')
                ->whereNotNull('submitted_at')
                ->whereBetween('submitted_at', [$startDate->startOfDay(), $endDate->endOfDay()])
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('count', 'date');
            
            $responseTrendsData = $dates->map(function ($date) use ($responseTrends) {
                return [
                    'date' => $date,
                    'count' => $responseTrends[$date] ?? 0,
                    'day_name' => \Carbon\Carbon::parse($date)->isoFormat('dddd')
                ];
            });
            
            return response()->json([
                'success' => true,
                'data' => [
                    'total_surveys' => $totalSurveys,
                    'total_responses' => $totalResponses,
                    'total_respondents' => $totalRespondents,
                    'unsent_responses' => $unsentResponses,
                    'survey_status' => $surveyStatus,
                    'verification_status' => $verificationStatus,
                    'latest_responses' => $latestResponses,
                    'popular_surveys' => $popularSurveys,
                    'response_trends' => $responseTrendsData
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch dashboard statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
