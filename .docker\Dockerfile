ARG CI_REGISTRY_IMAGE=changeme
ARG BUILD_VERSION=changeme
ARG APP_URL=changeme
ARG APP_NAME=changeme
ARG APP_ENV=development

FROM docker.io/composer:lts AS vendor
WORKDIR /app

ARG APP_ENV
ENV APP_ENV=${APP_ENV}
COPY composer.* ./
RUN if [ "$APP_ENV" = "production" ]; then \
      composer install --no-cache --no-scripts --no-progress --ignore-platform-reqs --no-interaction --prefer-dist --optimize-autoloader --no-dev; \
    else \
      composer install --no-cache --no-scripts --no-progress --ignore-platform-reqs --no-interaction --prefer-dist ; \
    fi

FROM docker.io/node:22.13.0 AS frontend
WORKDIR /app

ARG APP_URL BUILD_VERSION APP_NAME
ENV VITE_BUILD_VERSION=${BUILD_VERSION} \
    APP_URL=${APP_URL} \
    BUILD_VERSION=${BUILD_VERSION} \
    APP_NAME=${APP_NAME} \
    VITE_APP_NAME=${APP_NAME}

COPY *.json *.js ./
COPY --from=vendor /app/vendor/ /app/vendor/
RUN npm ci --silent --maxsockets=16

COPY public ./public
COPY resources ./resources
RUN npm run build

FROM $CI_REGISTRY_IMAGE:base AS release
WORKDIR /app
ARG APP_URL BUILD_VERSION APP_NAME
ENV VITE_BUILD_VERSION=${BUILD_VERSION} \
  APP_URL=${APP_URL} \
  BUILD_VERSION=${BUILD_VERSION} \
  APP_NAME=${APP_NAME} \
  VITE_APP_NAME=${APP_NAME}

COPY . .
COPY --from=frontend /app/resources /app/resources
COPY --from=frontend /app/public /app/public
COPY --from=vendor /app/vendor/ /app/vendor/

RUN php artisan storage:link
RUN php artisan config:clear