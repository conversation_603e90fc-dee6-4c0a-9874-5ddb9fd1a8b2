<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('surveys', function (Blueprint $table) {
            $table->dropColumn('full_instrument_code');
        });
    }

    public function down()
    {
        Schema::table('surveys', function (Blueprint $table) {
            $table->string('full_instrument_code')->after('end_date');
        });
    }
};
