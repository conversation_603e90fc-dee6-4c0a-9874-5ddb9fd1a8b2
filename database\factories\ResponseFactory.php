<?php

namespace Database\Factories;

use App\Models\Response;
use App\Models\Respondent;
use App\Models\Survey;
use Illuminate\Database\Eloquent\Factories\Factory;

class ResponseFactory extends Factory
{
    protected $model = Response::class;

    public function definition()
    {
        return [
            'respondent_id' => Respondent::factory(),
            'survey_id' => Survey::factory(),
            'submitted_at' => now()
        ];
    }
}
