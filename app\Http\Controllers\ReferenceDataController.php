<?php

namespace App\Http\Controllers;

use App\Models\RefData;
use App\Models\RefDataDetail;
use App\Exports\ReferenceDataTemplateExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRowImport;

class ReferenceDataController extends Controller
{
    /**
     * Display the reference data index page.
     */
    public function index()
    {
        return Inertia::render('ReferenceData/Index');
    }

    /**
     * Download the reference data import template.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadTemplate()
    {
        $fileName = 'template-import-referensi-' . date('Ymd-His') . '.xlsx';
        return Excel::download(new ReferenceDataTemplateExport, $fileName);
    }

    /**
     * Store a newly created Reference Data.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:255|unique:refdata,key',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ], [
            'key.required' => 'Key is required.',
            'key.unique' => 'Key already exists.',
            'title.required' => 'Title is required.',
        ]);

        try {
            $refData = RefData::create([
                'key' => $validated['key'],
                'title' => $validated['title'],
                'description' => $validated['description'] ?? null,
                'items' => 0,
            ]);

            return back()->with('success', 'Reference Data created successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create Reference Data.']);
        }
    }

    /**
     * Update the specified Reference Data.
     */
    public function update(Request $request, $key)
    {
        $refData = RefData::where('key', $key)->firstOrFail();

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ], [
            'title.required' => 'Title is required.',
        ]);

        try {
            $refData->update([
                'title' => $validated['title'],
                'description' => $validated['description'] ?? null,
            ]);

            return back()->with('success', 'Reference Data updated successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update Reference Data.']);
        }
    }

    /**
     * Soft delete the specified Reference Data.
     */
    public function destroy($key)
    {
        try {
            $refData = RefData::where('key', $key)->firstOrFail();

            $refData->delete();

            return back()->with('success', 'Reference Data deleted successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete Reference Data.']);
        }
    }

    /**
     * Restore a soft deleted Reference Data.
     */
    public function restore($key)
    {
        try {
            $refData = RefData::withTrashed()->where('key', $key)->firstOrFail();
            $refData->restore();

            return back()->with('success', 'Reference Data restored successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to restore Reference Data.']);
        }
    }

    /**
     * Permanently delete the specified Reference Data.
     */
    public function forceDelete($key)
    {
        try {
            DB::transaction(function () use ($key) {
                $refData = RefData::withTrashed()->where('key', $key)->firstOrFail();

                RefDataDetail::where('refdata_type', $key)->delete();

                $refData->forceDelete();
            });

            return back()->with('success', 'Reference Data permanently deleted.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to permanently delete Reference Data.']);
        }
    }

    /**
     * Provide data for the Reference Data table (API endpoint).
     */
    public function getData(Request $request)
    {
        try {
            $page = (int) ($request->input('page') ?? 1);
            $perPage = (int) ($request->input('size') ?? 10);
            $search = $request->input('search');
            $showTrashed = $request->input('show_trashed', false);

            $query = RefData::withTrashed();

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('key', 'like', "%{$search}%")
                      ->orWhere('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            if ($showTrashed === 'only') {
                $query->onlyTrashed();
            } elseif (!$showTrashed) {
                $query->whereNull('deleted_at');
            }

            $data = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['key', 'title', 'description', 'items', 'deleted_at'], 'page', $page);

            $items = $data->items();
            foreach ($items as $item) {
                $item->status = $item->deleted_at ? 'Deleted' : 'Active';
            }

            return response()->json([
                'page' => $data->currentPage(),
                'pageCount' => $data->lastPage(),
                'totalCount' => $data->total(),
                'data' => $items,
            ], 200);
        } catch (\Exception $e) {
            \Log::error('Error fetching reference data: ' . $e->getMessage(), ['exception' => $e]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch data: ' . $e->getMessage(),
            ], 500);
        }
    }
    /**
     * Show detail for a specific reference data type.
     */
    public function detail($key)
    {
        $reference = RefData::where('key', $key)->firstOrFail();

        $data = RefDataDetail::where('refdata_type', $key)
            ->select('ref_code', 'ref_name', 'ref_desc')
            ->get()
            ->toArray();

        return Inertia::render('ReferenceData/Detail', [
            'key' => $key,
            'title' => $reference->title,
            'data' => $data,
        ]);
    }

    /**
     * Provide detailed data for a specific reference data type (API endpoint).
     */
    public function getDetailData(Request $request, $key)
    {
        $refData = RefData::where('key', $key)->firstOrFail();
        
        $showTrashed = $request->input('show_trashed', false);

        $query = RefDataDetail::where('refdata_id', $refData->id)
            ->select('id', 'ref_code', 'ref_name', 'ref_desc', 'deleted_at');

        if ($showTrashed === 'only') {
            $query->onlyTrashed();
        } elseif ($showTrashed === true || $showTrashed === 'true' || $showTrashed === 'all') {
            $query->withTrashed();
        } else {
            $query->whereNull('deleted_at');
        }
        
        if ($search = $request->input('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('ref_code', 'like', "%{$search}%")
                  ->orWhere('ref_name', 'like', "%{$search}%")
                  ->orWhere('ref_desc', 'like', "%{$search}%");
            });
        }
        
        $page = (int) ($request->input('page') ?? 1);
        $perPage = (int) ($request->input('size') ?? 10);
        
        if ($sortField = $request->input('sort.field')) {
            $sortDir = $request->input('sort.sort', 'asc');
            $query->orderBy($sortField, $sortDir);
        } else {
            $query->orderBy('ref_code', 'asc');
        }

        $data = $query->paginate($perPage, ['*'], 'page', $page);
        
        return response()->json([
            'page' => $data->currentPage(),
            'pageCount' => $data->lastPage(),
            'totalCount' => $data->total(),
            'data' => $data->items(),
        ]);
    }

    /**
     * Store a newly created RefDataDetail.
     */
    public function storeDetail(Request $request, $parentKey)
    {
        $targetParentKey = $request->input('parent_data', $parentKey);
        $parentRefData = RefData::where('key', $targetParentKey)->firstOrFail();

        $validated = $request->validate([
            'ref_code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('refdata_details')->where(function ($query) use ($targetParentKey) {
                    return $query->where('refdata_type', $targetParentKey);
                }),
            ],
            'ref_name' => 'required|string|max:255',
            'ref_desc' => 'nullable|string|max:1000',
            'parent_data' => 'sometimes|string|exists:refdata,key',
        ], [
            'ref_code.required' => 'Code is required.',
            'ref_code.unique' => 'Code already exists for this reference type.',
            'ref_name.required' => 'Name is required.',
            'parent_data.exists' => 'Selected parent data does not exist.',
        ]);

        try {
            RefDataDetail::create([
                'refdata_type' => $targetParentKey,
                'refdata_id' => $parentRefData->id,
                'ref_code' => $validated['ref_code'],
                'ref_name' => $validated['ref_name'],
                'ref_desc' => $validated['ref_desc'] ?? null,
            ]);

            return back()->with('success', 'Detail item created successfully.');
        } catch (\Exception $e) {
            \Log::error('Error creating reference data detail: ' . $e->getMessage(), ['exception' => $e]);
            return back()->withErrors(['error' => 'Failed to create detail item. ' . $e->getMessage()]);
        }
    }

    /**
     * Update the specified RefDataDetail.
     */
    public function updateDetail(Request $request, $parentKey, $detailId)
    {
        $currentRefData = RefData::where('key', $parentKey)->firstOrFail();
        $detail = RefDataDetail::where('refdata_type', $parentKey)
                                ->findOrFail($detailId);

        $targetParentKey = $request->input('parent_data', $parentKey);
        $targetRefData = $targetParentKey === $parentKey 
            ? $currentRefData 
            : RefData::where('key', $targetParentKey)->firstOrFail();

        $validated = $request->validate([
            'ref_code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('refdata_details')->where(function ($query) use ($targetParentKey, $detail) {
                    return $query->where('refdata_type', $targetParentKey)
                                 ->where('id', '!=', $detail->id);
                }),
            ],
            'ref_name' => 'required|string|max:255',
            'ref_desc' => 'nullable|string|max:1000',
            'parent_data' => 'sometimes|string|exists:refdata,key',
        ], [
            'ref_code.required' => 'Code is required.',
            'ref_code.unique' => 'Code already exists for this reference type.',
            'ref_name.required' => 'Name is required.',
            'parent_data.exists' => 'Selected parent data does not exist.',
        ]);

        try {
            $updateData = [
                'refdata_type' => $targetParentKey,
                'refdata_id' => $targetRefData->id,
                'ref_code' => $validated['ref_code'],
                'ref_name' => $validated['ref_name'],
                'ref_desc' => $validated['ref_desc'] ?? null,
            ];

            $detail->update($updateData);
            return back()->with('success', 'Detail item updated successfully.');
        } catch (\Exception $e) {
            \Log::error('Error updating reference data detail: ' . $e->getMessage(), ['exception' => $e]);
            return back()->withErrors(['error' => 'Failed to update detail item. ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified RefDataDetail.
     */
    public function destroyDetail($parentKey, $detailId)
    {
        RefData::where('key', $parentKey)->firstOrFail();
        $detail = RefDataDetail::where('refdata_type', $parentKey)
                                ->findOrFail($detailId);

        try {
            $detail->delete();
            return back()->with('success', 'Detail item moved to trash.');
        } catch (\Exception $e) {
            \Log::error('Error soft deleting reference data detail: ' . $e->getMessage(), ['exception' => $e]);
            return back()->withErrors(['error' => 'Failed to move detail item to trash.']);
        }
    }

    /**
     * Restore a soft deleted RefDataDetail.
     */
    public function restoreDetail($parentKey, $detailId)
    {
        RefData::where('key', $parentKey)->firstOrFail();
        $detail = RefDataDetail::where('refdata_type', $parentKey)
                                ->withTrashed()
                                ->findOrFail($detailId);
        try {
            $detail->restore();
            return back()->with('success', 'Detail item restored successfully.');
        } catch (\Exception $e) {
            \Log::error('Error restoring reference data detail: ' . $e->getMessage(), ['exception' => $e]);
            return back()->withErrors(['error' => 'Failed to restore detail item.']);
        }
    }

    /**
     * Permanently delete the specified RefDataDetail.
     */
    public function forceDeleteDetail($parentKey, $detailId)
    {
        RefData::where('key', $parentKey)->firstOrFail();
        $detail = RefDataDetail::where('refdata_type', $parentKey)
                                ->withTrashed()
                                ->findOrFail($detailId);
        try {
            $detail->forceDelete();
            return back()->with('success', 'Detail item permanently deleted.');
        } catch (\Exception $e) {
            \Log::error('Error permanently deleting reference data detail: ' . $e->getMessage(), ['exception' => $e]);
            return back()->withErrors(['error' => 'Failed to permanently delete detail item.']);
        }
    }

     /**
     * Import reference data details from a CSV file.
     */
    public function importDetail(Request $request, $parentKey)
    {
        $parentRefData = RefData::where('key', $parentKey)->firstOrFail();

        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240',
        ]);

        $file = $request->file('file');
        $extension = strtolower($file->getClientOriginalExtension());
        $isExcel = in_array($extension, ['xlsx', 'xls']);
        $errors = [];
        $successCount = 0;
        $totalRows = 0;
        $expectedHeader = ['ref_code', 'ref_name', 'ref_desc'];

        DB::beginTransaction();
        $hasCriticalError = false;

        try {
            if ($isExcel) {
                $import = new class($parentRefData->id, $parentKey) {
                    private $parentId;
                    private $parentKey;
                    private $errors = [];
                    private $successCount = 0;
                    private $lineNumber = 1;

                    public function __construct($parentId, $parentKey) {
                        $this->parentId = $parentId;
                        $this->parentKey = $parentKey;
                    }

                    public function model(array $row) {
                        $this->lineNumber++;
                        
                        if (empty(array_filter($row))) {
                            return null;
                        }
                        $rowErrors = [];
                        if (empty(trim($row['ref_code'] ?? ''))) {
                            $rowErrors[] = 'Kode wajib diisi';
                        }
                        if (empty(trim($row['ref_name'] ?? ''))) {
                            $rowErrors[] = 'Nama wajib diisi';
                        }

                        if (!empty($rowErrors)) {
                            $this->errors[] = [
                                'row' => $this->lineNumber,
                                'message' => implode(', ', $rowErrors),
                                'data' => implode(', ', array_values($row))
                            ];
                            return null;
                        }

                        $exists = RefDataDetail::where('refdata_type', $this->parentKey)
                            ->where('ref_code', trim($row['ref_code']))
                            ->withTrashed()
                            ->exists();

                        if ($exists) {
                            $this->errors[] = [
                                'row' => $this->lineNumber,
                                'message' => "Kode '{$row['ref_code']}' sudah ada",
                                'data' => implode(', ', array_values($row))
                            ];
                            return null;
                        }

                        try {
                            RefDataDetail::create([
                                'refdata_type' => $this->parentKey,
                                'refdata_id' => $this->parentId,
                                'ref_code' => trim($row['ref_code']),
                                'ref_name' => trim($row['ref_name']),
                                'ref_desc' => !empty(trim($row['ref_desc'] ?? '')) ? trim($row['ref_desc']) : null,
                            ]);
                            $this->successCount++;
                        } catch (\Exception $e) {
                            $this->errors[] = [
                                'row' => $this->lineNumber,
                                'message' => 'Gagal menyimpan data: ' . $e->getMessage(),
                                'data' => implode(', ', array_values($row))
                            ];
                        }

                        return null;
                    }

                    public function getErrors() {
                        return $this->errors;
                    }

                    public function getSuccessCount() {
                        return $this->successCount;
                    }
                };

                $importData = Excel::toArray([], $file, null, \Maatwebsite\Excel\Excel::XLSX)[0] ?? [];

                $importData = array_values(array_filter($importData, function($row, $index) {
                    if ($index < 3) return false;
                    
                    $firstCell = trim($row[0] ?? '');
                    if (str_starts_with($firstCell, 'Petunjuk:') || 
                        str_starts_with($firstCell, '-')) {
                        return false;
                    }
                    
                    if (isset($row[0]) && $row[0] === 'ref_code') {
                        return false;
                    }
                    
                    return !empty(array_filter($row, function($value) { 
                        return $value !== null && $value !== ''; 
                    }));
                }, ARRAY_FILTER_USE_BOTH));

                $totalRows = count($importData);
                $successCount = 0;
                $errors = [];

                if (empty($importData)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'File kosong',
                        'errors' => ['file' => 'File yang diunggah tidak berisi data yang valid. Pastikan Anda mengisi data di bawah baris contoh.']
                    ], 422);
                }

                foreach ($importData as $index => $row) {
                    $lineNumber = $index + 4; 
                    
                    if (empty(array_filter($row, function($value) { return $value !== null && $value !== ''; }))) {
                        $totalRows--;
                        continue;
                    }
                    $rowErrors = [];
                    if (empty(trim($row[0] ?? ''))) {
                        $rowErrors[] = 'Kode wajib diisi';
                    }
                    if (empty(trim($row[1] ?? ''))) {
                        $rowErrors[] = 'Nama wajib diisi';
                    }

                    if (!empty($rowErrors)) {
                        $errors[] = [
                            'row' => $lineNumber,
                            'message' => implode(', ', $rowErrors),
                            'data' => implode(', ', $row)
                        ];
                        continue;
                    }

                    $exists = RefDataDetail::where('refdata_type', $parentKey)
                        ->where('ref_code', trim($row[0]))
                        ->withTrashed()
                        ->exists();

                    if ($exists) {
                        $errors[] = [
                            'row' => $lineNumber,
                            'message' => "Kode '{$row[0]}' sudah ada",
                            'data' => implode(', ', $row)
                        ];
                        continue;
                    }

                    try {
                        RefDataDetail::create([
                            'refdata_type' => $parentKey,
                            'refdata_id' => $parentRefData->id,
                            'ref_code' => trim($row[0]),
                            'ref_name' => trim($row[1]),
                            'ref_desc' => !empty(trim($row[2] ?? '')) ? trim($row[2]) : null,
                        ]);
                        $successCount++;
                    } catch (\Exception $e) {
                        $errors[] = [
                            'row' => $lineNumber,
                            'message' => 'Gagal menyimpan data: ' . $e->getMessage(),
                            'data' => implode(', ', $row)
                        ];
                        continue;
                    }
                }
            } else {
                $handle = fopen($file->getPathname(), 'r');
                
                if ($handle === false) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Gagal membuka file.',
                        'errors' => ['file' => 'Gagal membuka file.']
                    ], 422);
                }
                
                fgetcsv($handle);

                $header = fgetcsv($handle);
                
                $lowerHeader = array_map('strtolower', $header);
                $lowerExpected = array_map('strtolower', $expectedHeader);
                
                if ($lowerHeader !== $lowerExpected) {
                    fclose($handle);
                    
                    $isNumericHeaders = true;
                    foreach ($header as $h) {
                        if (!is_numeric($h)) {
                            $isNumericHeaders = false;
                            break;
                        }
                    }
                    
                    $errorMessage = $isNumericHeaders 
                        ? 'File yang diunggah memiliki header numerik. Pastikan untuk menggunakan template yang diunduh dari sistem.'
                        : 'Format header tidak valid. Pastikan menggunakan: ' . implode(', ', $expectedHeader);
                    
                    return response()->json([
                        'success' => false,
                        'message' => 'Format header tidak valid',
                        'errors' => ['file' => $errorMessage],
                        'debug' => [
                            'expected_headers' => $expectedHeader,
                            'actual_headers' => $header,
                            'header_string' => implode(',', $header),
                            'lower_expected' => $lowerExpected,
                            'lower_actual' => $lowerHeader,
                            'missing' => array_diff($lowerExpected, $lowerHeader),
                            'extra' => array_diff($lowerHeader, $lowerExpected)
                        ]
                    ], 422);
                }

                $lineNumber = 1;
                while (($row = fgetcsv($handle)) !== false) {
                    $lineNumber++;
                    $totalRows++;
                    
                    if (empty(array_filter($row, function($value) { return $value !== null && $value !== ''; }))) {
                        $totalRows--;
                        continue;
                    }

                    if (count($row) !== 3) {
                        $errors[] = [
                            'row' => $lineNumber,
                            'message' => 'Format data tidak valid',
                            'data' => implode(', ', $row)
                        ];
                        continue;
                    }


                    $data = array_combine($header, $row);
                    
                    $rowErrors = [];
                    if (empty(trim($data['ref_code']))) {
                        $rowErrors[] = 'Kode wajib diisi';
                    }
                    if (empty(trim($data['ref_name']))) {
                        $rowErrors[] = 'Nama wajib diisi';
                    }

                    if (!empty($rowErrors)) {
                        $errors[] = [
                            'row' => $lineNumber,
                            'message' => implode(', ', $rowErrors),
                            'data' => implode(', ', $row)
                        ];
                        continue;
                    }


                    $exists = RefDataDetail::where('refdata_type', $parentKey)
                        ->where('ref_code', trim($data['ref_code']))
                        ->withTrashed()
                        ->exists();

                    if ($exists) {
                        $errors[] = [
                            'row' => $lineNumber,
                            'message' => "Kode '{$data['ref_code']}' sudah ada",
                            'data' => implode(', ', $row)
                        ];
                        continue;
                    }

                    DB::beginTransaction();
                    
                    try {
                        RefDataDetail::create([
                            'refdata_type' => $parentKey,
                            'refdata_id' => $parentRefData->id,
                            'ref_code' => trim($data['ref_code']),
                            'ref_name' => trim($data['ref_name']),
                            'ref_desc' => !empty(trim($data['ref_desc'] ?? '')) ? trim($data['ref_desc']) : null,
                        ]);

                        DB::commit();
                        $successCount++;
                    } catch (\Exception $e) {
                        DB::rollBack();
                        $errors[] = [
                            'row' => $lineNumber,
                            'message' => 'Gagal menyimpan data: ' . $e->getMessage(),
                            'data' => implode(', ', $row)
                        ];
                        continue;
                    }
                }

                fclose($handle);
            }

            DB::commit();
            
            return response()->json([
                'success' => $successCount > 0,
                'total_success' => $successCount,
                'total_failed' => count($errors),
                'total_data' => $totalRows,
                'errors' => $errors
            ], $successCount > 0 ? 200 : 422);

        } catch (\Exception $e) {
            DB::rollBack();
            if (is_resource($handle)) {
                fclose($handle);
            }
            \Log::error('Error importing reference data: ' . $e->getMessage(), [
                'exception' => $e,
                'parent_key' => $parentKey,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan pada sistem: ' . $e->getMessage(),
                'errors' => ['system' => 'Terjadi kesalahan saat memproses file.']
            ], 500);
        }   
    }
}