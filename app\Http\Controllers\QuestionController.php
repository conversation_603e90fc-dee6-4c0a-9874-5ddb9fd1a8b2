<?php

namespace App\Http\Controllers;

use App\Exports\QuestionDataTemplate;
use App\Exports\QuestionImportTemplate;
use App\Http\Requests\QuestionRequest;
use App\Models\Instrument;
use App\Models\Question;
use App\Models\Survey;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;

class QuestionController extends Controller
{
    public function index()
    {
        return Inertia::render('BankSoal/IndexBankSoal');
    }

    public function create($survey)
    {
        $query = Survey::select('id', 'code', 'title', 'description', 'created_at', 'status')->with([
            'instruments' => function ($q) {
                $q->select(
                    'id',
                    'instrument_name',
                    'instrument_description',
                    'instrument_code',
                    'instrument_level',
                    'full_instrument_code',
                    'parent_id',
                    'survey_id',
                    'respondent_type'
                )->with([
                    'children' => function ($child) {
                        $child->select(
                            'id',
                            'instrument_name',
                            'instrument_description',
                            'instrument_code',
                            'instrument_level',
                            'full_instrument_code',
                            'parent_id',
                            'survey_id',
                            'respondent_type'
                        )->with([
                            'children' => function ($deep) {
                                $deep->select(
                                    'id',
                                    'instrument_name',
                                    'instrument_description',
                                    'instrument_code',
                                    'instrument_level',
                                    'full_instrument_code',
                                    'parent_id',
                                    'survey_id',
                                    'respondent_type'
                                )->with(['questions' => function ($q) {
                                    $q->select('id', 'survey_id', 'question_text', 'question_type', 'required', 'q_answer', 'position', 'instrument_id')
                                        ->orderBy('position');
                                }]);
                            },
                            'questions' => function ($q) {
                                $q->select('id', 'survey_id', 'question_text', 'question_type', 'required', 'q_answer', 'position', 'instrument_id')
                                    ->orderBy('position');
                            }
                        ]);
                    },
                    'questions' => function ($q) {
                        $q->select('id', 'survey_id', 'question_text', 'question_type', 'required', 'q_answer', 'position', 'instrument_id')
                            ->orderBy('position');
                    }
                ])->withCount('questions');
            }
        ])->withCount('questions')->find($survey);

        return Inertia::render(
            'SurveiManagement/IndexSurveiManageQuestions',
            $query
        );
    }

    public function getData(Request $request)
    {
        $query = Question::whereNull('instrument_id')
        ->select('id', 'question_text', 'question_type', 'required', 'q_answer', 'created_at', 'updated_at');

        if ($search = $request->input('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('question_text', 'like', "%{$search}%");
                $q->orWhere('question_type', 'like', "%{$search}%");
            });
        }

        $page = (int) ($request->input('page') ?? 1);
        $perPage = (int) ($request->input('size') ?? 10);
        $filters = $request->input('filters') ?? [];
        $decodedFilters = is_string($filters) ? json_decode($filters, true) : $filters;

        $typeFilter = collect($decodedFilters)->firstWhere('column', 'question_type');
        $type = $typeFilter['value'] ?? strtolower(trim($request->input('question_type', '')));
        
        if (!empty($type) && $type !== 'semua') {
            $query->where('question_type', $type);
        }

        if ($sortField = $request->input('sort.field')) {
            $sortDir = $request->input('sort.sort', 'asc');
            $query->orderBy($sortField, $sortDir);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $data = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'page' => $data->currentPage(),
            'pageCount' => $data->lastPage(),
            'totalCount' => $data->total(),
            'data' => $data->items(),
        ]);
    }

    public function store(QuestionRequest $request)
    {
        $question = DB::transaction(function () use ($request) {
            $instrument = Instrument::find($request->id_instrument);
            $lastPosInInstrument  = Question::where('instrument_id', $instrument->id)
                ->where('survey_id', $instrument->survey_id)
                ->lockForUpdate()
                ->max('position');

            $insertPosition = $lastPosInInstrument  + 1;

            Question::where('survey_id', $instrument->survey_id)
                ->where('position', '>=', $insertPosition)
                ->increment('position');

            $data = [
                'instrument_id' => $instrument->id,
                'survey_id' => $instrument->survey_id,
                'question_text' => $request->question,
                'question_type' => $request->type,
                'q_answer' => $request->q_answer ?? null,
                'required' => $request->required,
                'position' => $insertPosition,
            ];

            return Question::create($data);
        });

        if ($question) {
            return redirect()->route('survei-management.questions.create', ['survey' => $question->survey_id])->with('success', 'Question created successfully.');
        } else {
            return redirect()->route('survei-management.questions.create', ['survey' => $question->survey_id])->with('error', 'Failed to create question.');
        }
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,csv,xls',
        ], [
            'file.required' => 'File is required.',
            'file.mimes' => 'File must be an Excel file.',
        ]);

        $file = $request->file('file');
        $ext = strtolower($file->getClientOriginalExtension());
        $rows = [];

        if (in_array($ext, ['xls', 'xlsx'])) {
            $rows = Excel::toArray([], $file)[0];

            $headerLabelsAtas = ['pertanyaan', 'tipe', 'wajib diisi', 'pilihan jawaban', null, null, 'pengaturan linear scale', null, null, null];
            $headerLabelsBawah = [null, null, null, 'opsi jawaban satuan', 'opsi jawaban baris', 'opsi jawaban kolom', 'label minimal', 'nilai minimal', 'label maksimal', 'nilai maksimal'];
            $headerIndexAtas = -1;
            $headerIndexBawah = -1;

            foreach ($rows as $i => $row) {
                $rowLower = array_map(function ($cell) {
                    return strtolower(trim((string) $cell));
                }, $row);

                if (
                    count($rowLower) >= 9 &&
                    $rowLower[0] === $headerLabelsAtas[0] &&
                    $rowLower[1] === $headerLabelsAtas[1] &&
                    $rowLower[2] === $headerLabelsAtas[2] &&
                    $rowLower[3] === $headerLabelsAtas[3] &&
                    $rowLower[6] === $headerLabelsAtas[6]
                ) {
                    $headerIndexAtas = $i;
                }

                if (
                    count($rowLower) >= 9 &&
                    $rowLower[3] === $headerLabelsBawah[3] &&
                    $rowLower[4] === $headerLabelsBawah[4] &&
                    $rowLower[5] === $headerLabelsBawah[5] &&
                    $rowLower[6] === $headerLabelsBawah[6] &&
                    $rowLower[7] === $headerLabelsBawah[7] &&
                    $rowLower[8] === $headerLabelsBawah[8] &&
                    $rowLower[9] === $headerLabelsBawah[9]
                ) {
                    $headerIndexBawah = $i;
                    break;
                }
            }

            if ($headerIndexAtas !== -1 && $headerIndexBawah !== -1) {
                $dataRows = [];
                if ($headerIndexBawah !== -1) {
                    for ($i = $headerIndexBawah + 1; $i < count($rows); $i++) {
                        $row = $rows[$i];
                        if (count($row) < 4) continue;
    
                        $first = strtolower(trim($row[0] ?? ''));
                        if ($first === '' || $first === 'petunjuk:' || str_starts_with($first, '-')) break;
                        
                        $dataRows[] = $row;
                    }
                }
            } else {
                return response()->json([
                    'success_count' => 0,
                    'failure_count' => 0,
                    'total' => 0,
                    'errors' => [['row' => 1, 'message' => 'Template tidak sesuai', 'data' => []]],
                ]);
            }
        }

        if (count($dataRows) <= 0) {
            return response()->json([
                'success_count' => 0,
                'failure_count' => 0,
                'total' => 0,
                'errors' => [['row' => 1, 'message' => 'File kosong atau tidak ada data', 'data' => []]],
            ]);
        }
        
        $success = 0;
        $failed = 0;
        $errors = [];

        foreach ($dataRows as $idx => $row) {
            $data = [
                'question_text' => trim($row[0] ?? ''),
                'question_type' => trim($row[1] ?? ''),
                'required' => trim($row[2] ?? 'Tidak') === 'Ya',
                'q_answer' => trim($row[3] ?? '') === '' ? [] : array_map(function ($a) { return trim($a ?? ''); }, explode(',', trim($row[3] ?? ''))),
                'scale' => [
                    'min_label' => trim($row[6] ?? ''),
                    'min' => intval(trim($row[7] ?? '0')),
                    'max_label' => trim($row[8] ?? ''),
                    'max' => intval(trim($row[9] ?? '0')),
                ]
            ];

            if ($data['question_type'] === 'Multiple Choice Grid' || $data['question_type'] === 'Checkbox Grid') {
                $data['q_answer'] = [
                    'rows' => explode(',', trim($row[4] ?? '')),
                    'columns' => explode(',', trim($row[5] ?? '')),
                ];
            }

            if ($data['question_type'] === 'Linear Scale') {
                $data['q_answer'] = $data['scale'];
            }

            $validator = Validator::make($data, [
                'question_text' => 'required|string',
                'question_type' => 'required|string|in:Short Answer,Paragraph,Multiple Choice,Checkbox,Dropdown,Linear Scale,Multiple Choice Grid,Checkbox Grid,Date,Time',
                'required' => 'boolean',
                'q_answer' => 'required_if:question_type,Multiple Choice,Checkbox,Dropdown,Multiple Choice Grid,Checkbox Grid|array',
                'scale.min' => 'required_if:question_type,Linear Scale|numeric',
                'scale.min_label' => 'required_if:question_type,Linear Scale|string',
                'scale.max' => 'required_if:question_type,Linear Scale|numeric',
                'scale.max_label' => 'required_if:question_type,Linear Scale|string',
            ], [
                'question_text.required' => 'Pertanyaan harus diisi.',
                'question_type.required' => 'Tipe pertanyaan harus diisi.',
                'question_type.in' => 'Tipe pertanyaan tidak valid.',
                'q_answer.required_if' => 'Pilihan jawaban harus diisi apabila tipe pertanyaan adalah ' . $data['question_type'] . '.',
                'scale.min.required_if' => 'Nilai minimum harus diisi apabila tipe pertanyaan adalah Linear Scale.',
                'scale.min.numeric' => 'Nilai minimum harus berupa angka.',
                'scale.min_label.required_if' => 'Label nilai minimum harus diisi apabila tipe pertanyaan adalah Linear Scale.',
                'scale.min_label.string' => 'Label nilai minimum harus berupa string.',
                'scale.max.required_if' => 'Nilai maksimum harus diisi apabila tipe pertanyaan adalah Linear Scale.',
                'scale.max.numeric' => 'Nilai maksimum harus berupa angka.',
                'scale.max_label.required_if' => 'Label nilai maksimum harus diisi apabila tipe pertanyaan adalah Linear Scale.',
                'scale.max_label.string' => 'Label nilai maksimum harus berupa string.',
            ]);

            if ($validator->fails()) {
                $failed++;
                $errors[] = [
                    'row' => $idx + 2,
                    'message' => implode(', ', $validator->errors()->all()),
                    'data' => $row,
                ];
                continue;
            }

            DB::beginTransaction();
            try {
                Question::create([
                    'question_text' => $data['question_text'],
                    'question_type' => str_replace(' ', '_', strtolower($data['question_type'])),
                    'q_answer' => count($data['q_answer']) > 0 ? $data['q_answer'] : null,
                    'required' => $data['required'],
                ]);
                DB::commit();

                $success++;
            } catch (\Throwable $e) {
                DB::rollBack();

                $failed++;
                $errors[] = [
                    'row' => $idx + 2,
                    'message' => 'Gagal import: ' . $e->getMessage(),
                    'data' => $row,
                ];
            }
        }

        return response()->json([
            'success_count' => $success,
            'failure_count' => $failed,
            'total' => count($dataRows),
            'errors' => $errors,
        ]);
    }

    public function update(QuestionRequest $request, Question $question)
    {
        $question->update([
            'instrument_id' => $request->id_instrument,
            'question_text' => $request->question,
            'question_type' => $request->type,
            'q_answer' => $request->q_answer ?? null,
            'required' => $request->required,
        ]);


        return back()->with('success', 'Question updated successfully.');
    }

    public function updatePosition(Request $request, Question $question)
    {
        $request->validate([
            'to' => 'required|string',
        ]);

        $direction = $request->input('to');

        if (!in_array($direction, ['up', 'down'])) {
            return response()->json(['error' => 'Invalid direction'], 422);
        }

        DB::transaction(function () use ($question, $direction) {
            $instrumentQuestions = Question::where('survey_id', $question->survey_id)
                ->where('instrument_id', $question->instrument_id)
                ->orderBy('position')
                ->lockForUpdate()
                ->get();

            $index = $instrumentQuestions->search(fn($q) => $q->id === $question->id);

            if ($direction === 'up' && $index === 0) {
                throw ValidationException::withMessages(['position' => 'Cannot move up anymore']);
            }

            if ($direction === 'down' && $index === $instrumentQuestions->count() - 1) {
                throw ValidationException::withMessages(['position' => 'Cannot move down anymore']);
            }

            $swapIndex = $direction === 'up' ? $index - 1 : $index + 1;
            $swapQuestion = $instrumentQuestions[$swapIndex];

            [$question->position, $swapQuestion->position] = [$swapQuestion->position, $question->position];
            $question->save();
            $swapQuestion->save();

            $allSurveyQuestions = Question::where('survey_id', $question->survey_id)
                ->orderBy('position')
                ->lockForUpdate()
                ->get();

            $counter = 1;
            foreach ($allSurveyQuestions as $q) {
                $q->update(['position' => $counter++]);
            }
        });


        return response()->json(['message' => 'Position updated']);
    }

    public function updateJobInstrument(Request $request, Instrument $instrument)
    {

        $request->validate([
            'respondent_type' => 'required',
        ], [
            'respondent_type.required' => 'Job description is required.',
        ]);
    
        $respondent_input = $request->input('respondent_type');
        $respondent_type_value = '';
        
        if (is_array($respondent_input)) {
            $values = [];
            foreach ($respondent_input as $item) {
                if (is_string($item)) {
                    $values[] = $item;
                } elseif (is_array($item) && isset($item['code'])) {
                    $values[] = $item['code'];
                } elseif (is_object($item) && isset($item->code)) {
                    $values[] = $item->code;
                } elseif (is_scalar($item)) {
                    $values[] = (string) $item;
                }
            }
            $respondent_type_value = implode(',', array_filter($values));
        } elseif (is_string($respondent_input)) {
            $respondent_type_value = $respondent_input;
        } elseif (is_object($respondent_input) && method_exists($respondent_input, 'toArray')) {
            $array_value = $respondent_input->toArray();
            $respondent_type_value = is_array($array_value) ? 
                implode(',', array_filter($array_value, 'is_scalar')) : 
                (string) $array_value;
        } else {
            $respondent_type_value = (string) $respondent_input;
        }

        try {
            $instrument->respondent_type = $respondent_type_value;
            $instrument->save();
            
            \Log::info('Instrument updated successfully', [
                'instrument_id' => $instrument->id,
                'respondent_type' => $respondent_type_value
            ]);
            
            $response_data = [
                'message' => 'Job descriptions updated successfully.',
                'respondent_type_value' => $respondent_type_value
            ];
            
            if (is_array($respondent_input) || (is_object($respondent_input) && method_exists($respondent_input, 'toArray'))) {
                $response_data['respondent_types'] = is_array($respondent_input) ? $respondent_input : $respondent_input->toArray();
            }
        } catch (\Exception $e) {
            \Log::error('Error updating instrument', [
                'error' => $e->getMessage(),
                'instrument_id' => $instrument->id,
                'respondent_input' => $respondent_input,
                'respondent_type_value' => $respondent_type_value
            ]);
            
            return response()->json([
                'message' => 'Failed to update job descriptions.',
                'error' => $e->getMessage()
            ], 500);
        }
        
        return response()->json($response_data);
    }

    public function destroy(string $question)
    {
        $question = Question::findOrFail($question);
        $surveyId = $question->survey_id;
        $deletedPosition = $question->position;

        DB::transaction(function () use ($question, $surveyId, $deletedPosition) {
            $question->delete();

            Question::where('survey_id', $surveyId)
                ->where('position', '>', $deletedPosition)
                ->decrement('position');
        });

        if ($question->delete()) {
            return redirect()->route('survei-management.questions.create', ['survey' => $surveyId])->with('success', 'Question deleted successfully.');
        } else {
            return redirect()->route('survei-management.questions.create', ['survey' => $surveyId])->with('error', 'Failed to delete question.');
        }
    }

    public function downloadTemplate()
    {
        return Excel::download(new QuestionImportTemplate, 'Template_Import_Pertanyaan_Survei.xlsx');
    }
}
