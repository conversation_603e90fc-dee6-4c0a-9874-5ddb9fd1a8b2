<?php

namespace App\Http\Controllers;

use App\Models\Answer;
use App\Models\Respondent;
use App\Models\Response;
use Illuminate\Http\Request;
use App\Models\Survey;
use App\Models\Instrument;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;


class FormulirSurveiController extends Controller
{
    /**
     * Display the specified survey with its instruments and questions.
     */
    /**
     * Recursively format instrument and its children
     */
    private function formatInstruments($instruments)
    {
        return $instruments->map(function($instrument) {
            $data = [
                'id' => $instrument->id,
                'name' => $instrument->instrument_name,
                'description' => $instrument->instrument_description,
                'level' => $instrument->instrument_level,
                'code' => $instrument->instrument_code,
                'respondent_type' => $instrument->respondent_type,
                'questions' => $instrument->questions->sortBy('position')->map(function($question) {
                    $options = $question->q_answer ?? null;
                    $columns = null;
                    $rows = null;
                    $scale = null;

                    if (in_array($question->question_type, ['multiple_choice', 'checkbox', 'dropdown']) && is_array($options)) {
                        $options = collect($options)->map(function($opt, $idx) {
                            return ['id' => $idx + 1, 'text' => $opt];
                        })->toArray();
                    }

                    if (in_array($question->question_type, ['multiple_choice_grid', 'checkbox_grid'])) {
                        $gridSource = is_array($question->q_answer) ? $question->q_answer : [];
                        $columns = collect($gridSource['columns'] ?? [])->map(function($col, $idx) {
                            return ['id' => $idx + 1, 'text' => $col];
                        })->toArray();
                        $rows = collect($gridSource['rows'] ?? [])->map(function($row, $idx) {
                            return ['id' => $idx + 1, 'text' => $row];
                        })->toArray();
                    }
                    if ($question->question_type === 'linear_scale' && is_array($options)) {
                        $scale = $options;
                    }

                    return [
                        'id' => $question->id,
                        'text' => $question->question_text,
                        'type' => $question->question_type,
                        'options' => $options,
                        'columns' => $columns,
                        'rows' => $rows,
                        'scale' => $scale,
                        'answer' => $question->q_answer ?? null,
                        'q_answer' => $question->q_answer ?? null,
                        'required' => (bool) $question->required,
                        'position' => (int) $question->position
                    ];
                })->values(),
                'children' => []
            ];

            if ($instrument->relationLoaded('children') && $instrument->children->isNotEmpty()) {
                $data['children'] = $this->formatInstruments($instrument->children);
            }

            return $data;
        })->sortBy('instrument_level')->values();
    }

    /**
     * Display the specified survey with its instruments and questions.
     */
    public function show($id)
    {
        $survey = Survey::with([
            'instruments' => function($query) {
                return $query->with(['children' => function($q) {
                    return $q->with('questions')
                           ->orderBy('instrument_level')
                           ->orderBy('instrument_code');
                }, 'questions'])
                ->whereNull('parent_id')
                ->orderBy('instrument_level')
                ->orderBy('instrument_code');
            }
        ])->findOrFail($id);

        $formatted = [
            'id' => $survey->id,
            'uuid' => $survey->uuid, 
            'code' => $survey->code,
            'title' => $survey->title,
            'description' => $survey->description,
            'instruments' => $this->formatInstruments($survey->instruments),
        ];

        return Inertia::render('SurveiManagement/FormSurvei', [
            'survei' => $formatted,
            'isPublic' => false,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $idOrUuid)
    {
        Log::info('Starting respondent store', [
            'idOrUuid' => $idOrUuid,
            'request_data' => $request->all()
        ]);

        $validated = $request->validate([
            'email' => 'required|email',
            'born_date' => 'required|date',
            'job_type' => 'required|string|max:100'
        ]);

        try {
            $survey = Survey::where('uuid', $idOrUuid)
                ->orWhere('id', $idOrUuid)
                ->firstOrFail();
                
            Log::info('Found survey', [
                'survey_id' => $survey->id,
                'survey_uuid' => $survey->uuid,
                'survey_title' => $survey->title
            ]);
            
            $email = $validated['email'];
            
            $existingRespondent = Respondent::where('email', $email)
                ->where('survey_id', $survey->id)
                ->first();

            if ($existingRespondent) {
                if ($existingRespondent->born_date != $validated['born_date']) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Anda sebelumnya pernah mengisi survei "' . $survey->title . '" namun data yang Anda inputkan tidak sesuai. Mohon cek ulang Email, Tanggal Lahir untuk melanjutkan pengisian survei',
                        'has_completed' => true
                    ], 422);
                }

                $existingResponse = Response::where('respondent_id', $existingRespondent->id)
                    ->where('survey_id', $survey->id)
                    ->whereNotNull('submitted_at')
                    ->exists();

                if ($existingResponse) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Anda sudah menyelesaikan survei ini sebelumnya.',
                        'has_completed' => true
                    ], 422);
                }
            }

            $respondent = Respondent::firstOrNew([
                'email' => $email,
                'survey_id' => $survey->id
            ]);

            $isMember = \App\Models\User::where('email', $email)->exists();
            $userData = null;
            
            if ($isMember) {
                $user = \App\Models\User::with('profile')->where('email', $email)->first();
                $userData = [
                    'id' => $user->id,
                    'name' => $user->profile ? $user->profile->name : $user->name,
                    'email' => $user->email
                ];
            }

            $respondent->fill([
                'born_date' => $validated['born_date'],
                'job_type' => $validated['job_type'],
                'is_member' => $isMember,
                'survey_id' => $survey->id
            ]);
            
            $respondent->save();
            
            Log::info('Saved respondent', [
                'respondent_id' => $respondent->id,
                'email' => $respondent->email,
                'survey_id' => $respondent->survey_id,
                'born_date' => $respondent->born_date,
                'job_type' => $respondent->job_type
            ]);

            $responses = [];
            if ($respondent->exists) {
                $response = Response::where('respondent_id', $respondent->id)
                    ->where('survey_id', $survey->id)
                    ->first();
                
                if ($response) {
                    $answers = Answer::where('response_id', $response->id)
                        ->get(['question_id', 'answer']);
                    foreach ($answers as $answer) {
                        $question = \App\Models\Question::find($answer->question_id);
                        $responses[] = [
                            'question_id' => $answer->question_id,
                            'question_type' => $question ? $question->question_type : null,
                            'answer' => (in_array($question->question_type, ['multiple_choice_grid', 'checkbox_grid']) && is_string($answer->answer))
                                ? json_decode($answer->answer, true)
                                : $answer->answer,
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Data responden berhasil disimpan',
                'data' => array_merge($respondent->toArray(), [
                    'responses' => $responses,
                    'user_data' => $isMember ? $userData : ['email' => $email]
                ])
            ]);

        } catch (\Exception $e) {
            Log::error('Error in store method: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memproses data.',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    private function saveFile($file, $fileDir)
    {
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $fileName = pathinfo($originalName, PATHINFO_FILENAME);
        $uniqueFileName = $fileName . '_' . time() . '.' . $extension;
        return $file->storeAs($fileDir, $uniqueFileName);
    }

    private function decodeGridAnswer($answerValue, $questionType)
    {
        if (is_string($answerValue) && in_array($questionType, ['multiple_choice_grid', 'checkbox_grid'])) {
            $decoded = json_decode($answerValue, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }
        }
        return $answerValue;
    }

    private function handleSingleQuestion($validated, $request, $survey, $fileDir, $response)
    {
        $question = \App\Models\Question::find($validated['question_id']);
        $questionType = $question ? $question->question_type : null;
        $isRequired = $question ? $question->required : false;

        if ($questionType === 'file') {
            if ($isRequired && !$request->hasFile('file')) {
                return ['error' => response()->json([
                    'success' => false,
                    'message' => 'File wajib diisi untuk pertanyaan file.',
                    'errors' => ['file' => ['File wajib diisi']]
                ], 422)];
            }
        } else {
            if ($isRequired && (empty($validated['answer']) && $validated['answer'] !== '0')) {
                return ['error' => response()->json([
                    'success' => false,
                    'message' => 'Jawaban wajib diisi.',
                    'errors' => ['answer' => ['Jawaban wajib diisi']]
                ], 422)];
            }
        }
        if ($questionType === 'file' && $request->hasFile('file')) {
            if ($request->filled('delete_all_old_files')) {
                $dir = "answers/{$survey->uuid}/{$response->id}";
                \Storage::deleteDirectory($dir);
                \Storage::makeDirectory($dir);
            }
            $path = $this->saveFile($request->file('file'), $fileDir);
            $answerValue = $path;
            \App\Models\Answer::updateOrCreate(
                [
                    'response_id' => $response->id,
                    'question_id' => $validated['question_id']
                ],
                ['answer' => $answerValue]
            );
            return ['file_path' => $answerValue];
        } else {
            if ($questionType === 'file') {
                return null;
            }
            $answerValue = $this->decodeGridAnswer($validated['answer'] ?? null, $questionType);
            \App\Models\Answer::updateOrCreate(
                [
                    'response_id' => $response->id,
                    'question_id' => $validated['question_id']
                ],
                ['answer' => $answerValue]
            );
        }
        return null;
    }

    private function handleMultipleQuestions($validated, $request, $survey, $fileDir, $response)
    {
        foreach ($validated['answers'] as $idx => $answerData) {
            $question = \App\Models\Question::find($answerData['question_id']);
            $questionType = $question ? $question->question_type : null;
            $isRequired = $question ? $question->required : false;

            if ($questionType === 'file') {
                if ($isRequired && !$request->hasFile("answers.$idx.file") && empty($answerData['answer'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'File wajib diisi untuk pertanyaan file.',
                        'errors' => ["answers.$idx.file" => ['File wajib diisi']]
                    ], 422);
                }
                if ($request->hasFile("answers.$idx.file")) {
                    $path = $this->saveFile($request->file("answers.$idx.file"), $fileDir);
                    $answerValue = $path;
                } else if (array_key_exists('answer', $answerData) && is_string($answerData['answer']) && !empty($answerData['answer'])) {
                    $answerValue = $answerData['answer'];
                } else {
                    $existing = \App\Models\Answer::where('response_id', $response->id)
                        ->where('question_id', $answerData['question_id'])->first();
                    $answerValue = $existing ? $existing->answer : null;
                }
            } else if (array_key_exists('answer', $answerData)) {
                $answerValue = $this->decodeGridAnswer($answerData['answer'], $questionType);
            } else {
                $answerValue = null;
            }
            \App\Models\Answer::updateOrCreate(
                [
                    'response_id' => $response->id,
                    'question_id' => $answerData['question_id']
                ],
                ['answer' => $answerValue]
            );
        }
        return null;
    }

    /**
     * Submit answers for survey questions
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $surveyId
     * @return \Illuminate\Http\Response
     */
    public function submitQuestions(Request $request, $surveyUuid)
    {
        try {
            Log::info('Submit Draft Request', [
                'all_data' => $request->all(),
                'files' => $request->allFiles(),
                'has_file' => $request->hasFile('file'),
                'question_id' => $request->input('question_id'),
                'answer' => $request->input('answer'),
                'is_submit' => $request->input('is_submit'),
                'answers' => $request->input('answers')
            ]);
            $fileMime = 'pdf,jpg,jpeg,png,gif,webp,doc,docx,xls,xlsx,csv';
            $fileRule = 'nullable|file|max:2048|mimes:' . $fileMime;
            $rules = [
                'respondent_id' => 'required|exists:respondents,id',
                'answers' => 'required_without:question_id|array',
                'answers.*.question_id' => 'required_with:answers|exists:questions,id',
                'answers.*.answer' => 'nullable',
                'answers.*.file' => $fileRule,
                'question_id' => 'required_without:answers|exists:questions,id',
                'answer' => 'nullable',
                'file' => $fileRule,
                'is_submit' => 'sometimes'
            ];
            $validator = \Illuminate\Support\Facades\Validator::make($request->all(), $rules);
            $validator->after(function ($validator) use ($request) {
                if ($request->has('question_id')) {
                    $question = \App\Models\Question::find($request->input('question_id'));
                    if ($question) {
                        if ($question->question_type === 'file') {
                            if ($question->required && !$request->hasFile('file')) {
                                $validator->errors()->add('file', 'File wajib diisi untuk pertanyaan file.');
                            }
                        } else {
                            if ($question->required && (empty($request->input('answer')) && $request->input('answer') !== '0')) {
                                $validator->errors()->add('answer', 'Jawaban wajib diisi.');
                            }
                        }
                    }
                }
                if ($request->has('answers') && is_array($request->input('answers'))) {
                    foreach ($request->input('answers') as $idx => $answerData) {
                        if (isset($answerData['question_id'])) {
                            $question = \App\Models\Question::find($answerData['question_id']);
                            if ($question && $question->required) {
                                if ($question->question_type === 'file') {
                                    if (!$request->hasFile("answers.$idx.file")) {
                                        $validator->errors()->add("answers.$idx.file", 'File wajib diisi untuk pertanyaan file.');
                                    }
                                } else {
                                    if (!array_key_exists('answer', $answerData) || (empty($answerData['answer']) && $answerData['answer'] !== '0')) {
                                        $validator->errors()->add("answers.$idx.answer", 'Jawaban wajib diisi.');
                                    }
                                }
                            }
                        }
                    }
                }
            });
            $validated = $validator->validate();
            $survey = Survey::where('uuid', $surveyUuid)->firstOrFail();
            DB::beginTransaction();
            $response = Response::firstOrCreate(
                [
                    'respondent_id' => $validated['respondent_id'],
                    'survey_id' => $survey->id
                ],
                ['submitted_at' => null]
            );
            $fileDir = "answers/{$survey->uuid}/{$response->id}";
            $filePath = null;
            if (isset($validated['question_id'])) {
                $result = $this->handleSingleQuestion($validated, $request, $survey, $fileDir, $response);
                if ($result && isset($result['error'])) return $result['error'];
                if ($result && isset($result['file_path'])) $filePath = $result['file_path'];
            } elseif (isset($validated['answers']) && is_array($validated['answers'])) {
                $result = $this->handleMultipleQuestions($validated, $request, $survey, $fileDir, $response);
                if ($result) return $result;
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid: diperlukan question_id atau answers array'
                ], 422);
            }
            if ($request->boolean('is_submit')) {
                $response->update(['submitted_at' => now()]);
            }
            DB::commit();
            if ($filePath) {
                return response()->json([
                    'success' => true,
                    'message' => 'File berhasil diupload',
                    'file_path' => $filePath,
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => 'Jawaban berhasil disimpan',
                'is_submitted' => (bool) $response->submitted_at
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Submit Draft Validation Error: ' . $e->getMessage(), ['errors' => $e->errors()]);
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Submit Draft Error: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}