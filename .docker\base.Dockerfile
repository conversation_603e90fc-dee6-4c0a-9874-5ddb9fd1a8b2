FROM docker.io/dunglas/frankenphp:php8.4.7
ENV TZ="Asia/Jakarta"
ENV COMPOSER_ALLOW_SUPERUSER=1
ENV PHP_INI_SCAN_DIR=":$PHP_INI_DIR/app.conf.d"

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY .docker/config/10-custom.ini $PHP_INI_DIR/app.conf.d/
COPY .docker/config/Caddyfile /etc/frankenphp/Caddyfile

RUN apt update && apt-get install -y --no-install-recommends \
  libpng-dev libjpeg-dev libwebp-dev libfreetype6-dev zlib1g-dev \
  && rm -rf /var/lib/apt/lists/*

RUN docker-php-ext-configure gd --enable-gd --with-freetype --with-jpeg --with-webp
RUN install-php-extensions \
  intl mbstring exif pcntl bcmath gd zip opcache redis pdo pdo_mysql mysqli