<template>
    <div class="h-screen bg-gray-50 flex !flex items-center !items-center justify-center !justify-center p-4">
      <div class="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
        <div class="flex justify-center mb-6">
          <div class="w-20 h-20 text-red-500">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
          </div>
        </div>
        <h1 class="text-2xl font-bold text-gray-900 mb-4">Ma<PERSON> Berlaku Survei Telah Berakhir</h1>
        
        <div v-if="survey" class="mb-6 text-center"> <!-- Tambahkan text-center di sini -->
          <p class="text-gray-700 mb-2">
            <span class="font-semibold">Judul Survei:</span> {{ survey.title }}
          </p>
          <p v-if="survey.start_date" class="text-gray-700 mb-2">
            <span class="font-semibold">Tanggal Mulai:</span> {{ survey.start_date }}
          </p>
          <p v-if="survey.end_date" class="text-gray-700 mb-4">
            <span class="font-semibold">Tanggal Berakhir:</span> {{ survey.end_date }}
          </p>
          <p class="text-gray-600">
            Mohon maaf, survei ini sudah tidak dapat diakses karena melewati masa berlaku yang ditentukan.
          </p>
        </div>
        
        <div class="flex flex-col sm:flex-row justify-center gap-3 mt-6">
        </div>
        
        <div class="mt-8 pt-4 border-t border-gray-200">
          <p class="text-sm text-gray-500">
            © {{ new Date().getFullYear() }} - Survey SPK
          </p>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps } from 'vue';
  
  defineProps({
    survey: {
      type: Object,
      default: () => ({
        title: '<EMAIL>',
        start_date: '16 July 2025 08:59',
        end_date: '16 July 2025 08:59'
      })
    }
  });
  </script>
  
  <style scoped>
  div.h-screen {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  </style>