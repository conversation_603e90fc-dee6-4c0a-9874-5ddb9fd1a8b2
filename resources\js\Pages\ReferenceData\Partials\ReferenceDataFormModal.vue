<script setup lang="ts">
import { defineProps, defineEmits, ref, watch, nextTick } from 'vue';
import { useForm } from '@inertiajs/vue3';
import TextInput from '@/Components/Partials/TextInput.vue';
import InputLabel from '@/Components/Partials/InputLabel.vue';
import InputError from '@/Components/Partials/InputError.vue';
import Modal from '@/Components/Partials/Modal.vue';
import Checkbox from '@/Components/Partials/Checkbox.vue';
import SelectInput from '@/Components/Partials/SelectInput.vue';

interface FormSchema {
  key?: string;
  title?: string;
  description?: string;
  ref_code?: string;
  ref_name?: string;
  ref_desc?: string;
  link_parent?: boolean;
  parent_data?: string;
  [key: string]: any;
}

interface Props {
  show: boolean;
  isEditMode: boolean;
  formObject: ReturnType<typeof useForm<FormSchema>>;
  itemType: 'parent' | 'detail';
  processing: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['submit', 'close']);

const referenceDataOptions = ref<Array<{value: string, label: string}>>([]);
const isLoading = ref(false);

const fetchReferenceData = async () => {
  try {
    isLoading.value = true;
    const response = await fetch(route('reference-data.get-data'));
    const data = await response.json();
    
    if (data && data.data) {
      referenceDataOptions.value = data.data.map((item: any) => ({
        value: item.key,
        label: `${item.key} - ${item.title}`
      }));
    }
  } catch (error) {
    console.error('Error fetching reference data:', error);
  } finally {
    isLoading.value = false;
  }
};

import { onMounted } from 'vue';
onMounted(() => {
  fetchReferenceData();
});

const keyInputRef = ref<HTMLInputElement | null>(null);
const codeInputRef = ref<HTMLInputElement | null>(null);
const titleInputRef = ref<HTMLInputElement | null>(null);
const linkParentRef = ref<HTMLInputElement | null>(null);

onMounted(() => {
  const modalEl = document.getElementById('modal-reference-data');
  if (modalEl) {
  } else {
  }
});

watch(() => props.show, (newValue) => {
  const modalEl = document.getElementById('modal-reference-data');
  if (modalEl) {
  } else {
    console.warn('[ReferenceDataFormModal] Modal DOM element NOT found!');
  }
  if (typeof window !== 'undefined' && window.KTModal && modalEl) {
    const modalInstance = window.KTModal.getInstance(modalEl) || new window.KTModal(modalEl);
    if (newValue) {
      modalInstance.show();
    } else {
      modalInstance.hide();
    }
  } else {
    if (window && !window.KTModal) {
    }
  }
  if (newValue) {
    nextTick(() => {
      if (props.itemType === 'parent' && !props.isEditMode) {
        keyInputRef.value?.focus();
      } else if (props.itemType === 'detail') {
        codeInputRef.value?.focus();
      } else {
        titleInputRef.value?.focus();
      }
    });
  }
});

const handleSubmit = () => {
  if (props.formObject.link_parent && props.formObject.parent_data) {
    const formData = { ...props.formObject };
    formData.parent_data = formData.parent_data;
    emit('submit', formData);
  } else {
    const { parent_data, ...formWithoutParent } = props.formObject;
    emit('submit', formWithoutParent);
  }
};

function handleClose() {
  emit('close');
}
</script>

<template>
  <Modal
    id="modal-reference-data"
    :modal-title="props.itemType === 'parent' ? (props.isEditMode ? 'Edit Data Referensi' : 'Tambah Data Referensi') : (props.isEditMode ? 'Edit Detail Item' : 'Tambah Detail Item')"
    :is-close-button="false"
    text-close-button="Batal"
    max-width="max-w-md"
    @close="handleClose"
  >
    <form id="form-reference-data" @submit.prevent="handleSubmit">
      <!-- Fields for Parent Reference Data -->
      <div v-if="props.itemType === 'parent'">
        <div class="mb-4" v-if="!props.isEditMode">
            <div class="relative">
              <InputLabel for="key" value="Key" />
              <TextInput
                id="key"
                v-model="props.formObject.key"
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': props.formObject.errors.key }"
                ref="keyInputRef"
                @input="props.formObject.clearErrors('key')"
              />
              <div v-if="props.formObject.errors.key" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2z" clip-rule="evenodd" />
                </svg>
              </div>
              <InputError :message="props.formObject.errors.key" class="mt-1 text-sm text-red-600" />
            </div>
          </div>
          <div class="mb-4">
            <div class="relative">
              <InputLabel for="title" value="Judul" />
              <TextInput
                id="title"
                v-model="props.formObject.title"
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': props.formObject.errors.title }"
                ref="titleInputRef"
                @input="props.formObject.clearErrors('title')"
              />
              <div v-if="props.formObject.errors.title" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2z" clip-rule="evenodd" />
                </svg>
              </div>
              <InputError :message="props.formObject.errors.title" class="mt-1 text-sm text-red-600" />
            </div>
          </div>
          <div class="mb-4">
            <InputLabel for="description" value="Deskripsi" />
            <TextInput
              id="description"
              v-model="props.formObject.description"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': props.formObject.errors.description }"
            />
            <InputError :message="props.formObject.errors.description" class="mt-2" />
          </div>
      </div>

      <!-- Fields for Detail Item -->
      <div v-else-if="props.itemType === 'detail'">
        <div class="mb-4">
          <div class="relative">
            <InputLabel for="ref_code" value="Kode" />
            <TextInput
              id="ref_code"
              v-model="props.formObject.ref_code"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': props.formObject.errors.ref_code }"
              ref="codeInputRef"
              @input="props.formObject.clearErrors('ref_code')"
            />
            <div v-if="props.formObject.errors.ref_code" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2z" clip-rule="evenodd" />
              </svg>
            </div>
            <InputError :message="props.formObject.errors.ref_code" class="mt-1 text-sm text-red-600" />
          </div>
          </div>
          <div class="mb-4">
            <div class="relative">
              <InputLabel for="ref_name" value="Nama" />
              <TextInput
                id="ref_name"
                v-model="props.formObject.ref_name"
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': props.formObject.errors.ref_name }"
                ref="titleInputRef"
                @input="props.formObject.clearErrors('ref_name')"
              />
              <div v-if="props.formObject.errors.ref_name" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2z" clip-rule="evenodd" />
                </svg>
              </div>
              <InputError :message="props.formObject.errors.ref_name" class="mt-1 text-sm text-red-600" />
            </div>
          </div>
          <div class="mb-4">
            <InputLabel for="ref_desc" value="Deskripsi" />
            <TextInput
              id="ref_desc"
              v-model="props.formObject.ref_desc"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': props.formObject.errors.ref_desc }"
            />
            <InputError :message="props.formObject.errors.ref_desc" class="mt-2" />
          </div>

          <!-- Hubungkan Data Induk -->
          <div class="mb-4">
            <div class="flex items-center">
              <Checkbox
                id="link_parent"
                v-model:checked="props.formObject.link_parent"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <InputLabel for="link_parent" value="Hubungkan Data Induk" class="ml-2 block text-sm font-medium text-gray-700" />
            </div>
            <InputError :message="props.formObject.errors.link_parent" class="mt-1" />
          </div>

          <!-- Data Induk (Conditional) -->
          <div v-if="props.formObject.link_parent" class="mb-4">
            <SelectInput
              v-model="props.formObject.parent_data"
              :options="referenceDataOptions"
              placeholder="Pilih Data Induk"
              class="mt-2"
              :error="props.formObject.errors.parent_data"
              :required="true"
              :disabled="isLoading"
              :loading="isLoading"
              label="Data Induk"
              name="parent_data"
            >
              <option value="" disabled>Pilih Data Induk</option>
              <option 
                v-for="(option, index) in referenceDataOptions" 
                :key="index" 
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </SelectInput>
            <InputError :message="props.formObject.errors.parent_data" class="mt-1" />
          </div>
      </div>


    </form>
    <template #actions>
      <button
        type="button"
        class="btn btn-light dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500"
        @click="handleClose"
      >Batal</button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="props.processing"
        form="form-reference-data"
      >
        {{ props.processing ? (props.isEditMode ? 'Menyimpan...' : 'Menambahkan...') : (props.isEditMode ? 'Simpan Perubahan' : 'Tambah') }}
      </button>
    </template>
  </Modal>
</template>