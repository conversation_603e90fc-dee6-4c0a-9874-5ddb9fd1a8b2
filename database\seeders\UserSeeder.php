<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Profile;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            'superadmin',
            'pengelola',
            'responden_anggota'
        ];

        foreach ($roles as $role) {
            if (!User::where('role', $role)->exists()) {
                $plainPassword = match (App::environment()) {
                    'local' => 'password',
                    'production' => 'Gaspol@SurveiSPK2025!',
                    default => 'SurveiSPK2025',
                };

                $user = User::create([
                    'email' => "$<EMAIL>",
                    'email_verified_at' => now(),
                    'role' => $role,
                    'password' => Hash::make($plainPassword),
                ]);

                if (App::environment('local') || App::environment('development')) {
                    Profile::factory()->create([
                        'user_id' => $user->id,
                    ]);
                } else {
                    Profile::create([
                        'user_id' => $user->id,
                        'name' => $role,
                        'phone_number' => '',
                        'department' => $role,
                        'position' => $role,
                        'address' => '',
                    ]);
                }

                echo "Membuat User $role, dengan $user->email - $plainPassword \n";
            }
        }
    }
}
