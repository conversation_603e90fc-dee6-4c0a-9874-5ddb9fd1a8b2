<?php

namespace Database\Factories;

use App\Models\Survey;
use Illuminate\Database\Eloquent\Factories\Factory;

class SurveyFactory extends Factory
{
    protected $model = Survey::class;

    public function definition()
    {
        return [
            'code' => 'SURV-' . strtoupper(uniqid()),
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph,
            'status' => $this->faker->randomElement(['aktif', 'nonaktif']),
            'link' => 'http://example.com/survey/' . $this->faker->regexify('[A-Za-z0-9]{10}'),
            'created_by' => 1,
        ];
    }
}
