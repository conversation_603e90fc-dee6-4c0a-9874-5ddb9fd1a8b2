<script setup lang="ts">
import InputError from "@/Components/Partials/InputError.vue";
import InputLabel from "@/Components/Partials/InputLabel.vue";
import PrimaryButton from "@/Components/Partials/PrimaryButton.vue";
import TextInput from "@/Components/Partials/TextInput.vue";
import InputFileImage from "@/Components/Partials/InputFileImage.vue";
import Toast from "@/Components/Partials/Toast.vue";
import { ref, computed } from "vue";
import { useForm, usePage } from "@inertiajs/vue3";

const user = usePage().props.auth.user;
const toastRef = ref<InstanceType<typeof Toast> | null>(null);
const requiredName = ref(false);
const requiredPhone = ref(false);
const fileSizeError = ref<string | null>(null);
const MAX_FILE_SIZE = 2 * 1024 * 1024;

const form = useForm({
  name: user.profile?.name,
  email: user.email,
  role: user.role,
  avatar: null,
  phone: user.profile?.phone_number,
  department: user.profile?.department,
  position: user.profile?.position,
  _method: "put",
});

const avatarPreview = ref(
  user.profile?.avatar
    ? "/storage/" + user.profile.avatar
    : "/assets/media/avatars/blank.png"
);

const validateFileSize = (file: File | null) => {
  if (!file) {
    fileSizeError.value = null;
    return true;
  }

  if (file.size > MAX_FILE_SIZE) {
    fileSizeError.value = "Ukuran gambar terlalu besar (maksimal 2MB)";
    return false;
  }

  fileSizeError.value = null;
  return true;
};

const isFormValid = computed(() => {
  return (
    form.name &&
    form.email &&
    emailValid.value &&
    form.phone &&
    phoneValid.value
  );
});

const validateName = () => {
  requiredName.value = true;
};

const validatePhone = () => {
  requiredPhone.value = true;
};

const emailValid = computed(() => {
  if (!form.email) return true;
  const emailPattern = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/;
  return emailPattern.test(form.email);
});

const phoneValid = computed(() => {
  if (!form.phone) return true;
  const phonePattern = /^\+?\d{10,}$/;
  return phonePattern.test(form.phone);
});

const submit = () => {
  if (form.avatar && !validateFileSize(form.avatar)) {
    toastRef.value?.show(
      "error",
      fileSizeError.value || "Ukuran gambar tidak valid"
    );
    return;
  }
  
  form.post(route("profile.update"), {
    forceFormData: true,
    onSuccess: () => {
      let newAvatarPath = user.profile?.avatar;
      if (form.avatar) {
        newAvatarPath = URL.createObjectURL(form.avatar);
      } else if (user.profile?.avatar) {
        newAvatarPath = '/storage/' + user.profile.avatar;
      }
      
      toastRef.value?.show("success", "Edit Profile Berhasil");
      
      window.dispatchEvent(
        new CustomEvent("profile-updated", {
          detail: {
            name: form.name,
            avatar: newAvatarPath
          },
        })
      );
    },
    onError: () => {
      if (toastRef.value) {
        toastRef.value.show("error", "Profile gagal diupdate");
      }
    },
  });
};
</script>

<template>
  <section>
    <div class="relative">
      <h2 class="text-lg text-black mt-0 mb-0 leading-tight relative -top-3">
        Informasi Pribadi
      </h2>
    </div>
    <div class="-mx-4 sm:-mx-8 border-b border-gray-200 mb-6"></div>
    <div class="mt-4 flex items-start">
      <div class="relative mr-6">
        <InputFileImage
          id="profile-image-input"
          :fotoDefault="avatarPreview || '/assets/media/avatars/blank.png'"
          class="!static !w-fit !h-fit"
          class-image-input="!size-24 mt-2"
          :isRoundedFull="true"
          v-model="form.avatar"
        />
      </div>
      <div>
        <h3 class="text-xl font-bold text-black">{{ form.name }}</h3>
        <p class="text-gray-600">{{ form.email }}</p>
        <p class="text-gray-600">Role: {{ user.role }}</p>
      </div>
    </div>

    <form
      @submit.prevent="submit"
      class="mt-6 space-y-6"
      id="form-edit-profile"
    >
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <InputLabel for="name" value="Nama Lengkap" />
          <TextInput
            id="name"
            type="text"
            class="mt-1 block w-full text-black"
            v-model="form.name"
            required
            autocomplete="name"
            @blur="validateName"
          />
          <InputError
            class="mt-2"
            :message="
              form.errors.name ||
              (requiredName && !form.name ? 'Nama lengkap wajib diisi' : '')
            "
          />
        </div>

        <div>
          <InputLabel for="email" value="Email" />
          <TextInput
            id="email"
            type="email"
            class="mt-1 block w-full text-black"
            v-model="form.email"
            required
            autocomplete="username"
            @input="form.clearErrors('email')"
          />
          <InputError
            class="mt-2"
            :message="
              !emailValid && form.email
                ? 'Format email tidak valid'
                : form.errors.email
            "
          />
        </div>

        <div>
          <InputLabel for="phone" value="Nomor Telepon" />
          <TextInput
            id="phone"
            type="text"
            class="mt-1 block w-full text-black"
            v-model="form.phone"
            autocomplete="tel"
            @blur="validatePhone"
            @input="form.clearErrors('phone')"
          />
          <InputError
            class="mt-2"
            :message="
              form.errors.phone ||
              (!form.phone && requiredPhone
                ? 'Nomor telepon wajib diisi'
                : form.phone && !phoneValid
                ? 'Format nomor telepon tidak sesuai'
                : '')
            "
          />
        </div>

        <div>
          <InputLabel for="department" value="Departemen" />
          <TextInput
            id="department"
            type="text"
            class="mt-1 block w-full text-black"
            v-model="form.department"
          />
          <InputError class="mt-2" :message="form.errors.department" />
        </div>

        <div class="md:col-span-2">
          <InputLabel for="position" value="Jabatan" />
          <TextInput
            id="position"
            type="text"
            class="mt-1 block w-full text-black"
            v-model="form.position"
          />
          <InputError class="mt-2" :message="form.errors.position" />
        </div>
      </div>

      <div>
        <PrimaryButton
          :disabled="
            form.processing || (form.email && !emailValid) || !isFormValid
          "
          class="bg-blue-600 hover:bg-blue-700 font-medium py-3 px-6 rounded-md"
        >
          Simpan Perubahan
        </PrimaryButton>
      </div>
    </form>
  </section>
  <Toast ref="toastRef" />
</template>
