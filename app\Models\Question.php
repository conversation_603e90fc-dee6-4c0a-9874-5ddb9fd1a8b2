<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;
    protected $connection = 'mysql';
    protected $table = 'questions';

    protected $fillable = [
        'survey_id',
        'instrument_id',
        'question_text',
        'question_type',
        'options',
        'q_answer',
        'required',
        'position',
    ];

    protected $casts = [
        'options' => 'json',
        'q_answer' => 'json',
        'required' => 'boolean',
    ];

    public function survey()
    {
        return $this->belongsTo(Survey::class);
    }

    public function instrument()
    {
        return $this->belongsTo(Instrument::class)->withTrashed();
    }
}
