<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('surveys', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('status', ['aktif', 'nonaktif'])->default('nonaktif');
            $table->string('full_instrument_code');
            $table->string('link')->nullable();
            $table->foreignId('created_by')
                  ->constrained('users')
                  ->name('fk_surveys_created_by');
            $table->timestamps();
            
            $table->index('code', 'idx_surveys_code');
            $table->index('status', 'idx_surveys_status');
            $table->index('created_by', 'idx_surveys_created_by');
        });
    }

    public function down()
    {
        Schema::dropIfExists('surveys');
    }
};
