<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class CombinedAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // dd('CombinedAuth middleware triggered', Auth::check(), Auth::guard('web')->check(), Auth::guard('spk')->check());
        if (Auth::guard('web')->check()) {
            Auth::shouldUse('web');
        } elseif (Auth::guard('spk')->check()) {
            Auth::shouldUse('spk');
        } else {
            return redirect()->route('login');
        }

        return $next($request);
    }
}
