<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ReferenceDataTemplateExport implements FromArray, WithHeadings, WithTitle, ShouldAutoSize, WithEvents
{
    /**
     * @return array
     */
    public function array(): array
    {
        $data = [
            ['REF001', 'Contoh Data 1', 'Deskripsi contoh data 1'],
            ['REF002', 'Contoh Data 2', 'Deskripsi contoh data 2'],
        ];

        for ($i = 3; $i <= 10; $i++) {
            $code = 'REF' . str_pad($i, 3, '0', STR_PAD_LEFT);
            $name = 'Data Contoh ' . $i;
            $desc = 'Ini adalah deskripsi untuk data contoh ' . $i;
            $data[] = [$code, $name, $desc];
        }

        return $data;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            ['TEMPLATE IMPOR DATA REFERENSI'],
            [],
            [
                'ref_code',
                'ref_name', 
                'ref_desc'
            ]
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Template';
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->mergeCells('A1:C1');
                $event->sheet->getStyle('A1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 14,
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                    ],
                ]);

                $event->sheet->getStyle('A3:C3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF'],
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '4472C4'],
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => '000000'],
                        ],
                    ],
                ]);

                $dataRowCount = count($this->array());
                if ($dataRowCount > 0) {
                    $event->sheet->getStyle('A4:C' . ($dataRowCount + 3))->applyFromArray([
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000'],
                            ],
                        ],
                    ]);
                }

                $event->sheet->getColumnDimension('A')->setWidth(20);
                $event->sheet->getColumnDimension('B')->setWidth(30);
                $event->sheet->getColumnDimension('C')->setWidth(40);

                $instructionRow = $dataRowCount + 5;
                $event->sheet->setCellValue('A' . $instructionRow, 'Petunjuk:');
                $event->sheet->setCellValue('A' . ($instructionRow + 1), '- Jangan mengubah posisi tabel');
                $event->sheet->setCellValue('A' . ($instructionRow + 2), '- Jangan mengubah nama kolom header');
                $event->sheet->setCellValue('A' . ($instructionRow + 3), '- Pastikan format file tetap .xlsx');
                $event->sheet->setCellValue('A' . ($instructionRow + 4), '- Data yang diimpor akan divalidasi sesuai format');
            },
        ];
    }
}