<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Survey;
use App\Models\Question;
use App\Models\Instrument;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SurveiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return Inertia::render('SurveiManagement/IndexSurveiManagement');
    }

    /**
     * Store a newly created survey with its instruments.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:aktif,nonaktif',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'instruments' => 'required|array|min:1',
            'instruments.*.instrument_name' => 'required|string|max:255',
            'instruments.*.instrument_description' => 'nullable|string',
            'instruments.*.instrument_code' => 'required|string|max:50',
            'instruments.*.instrument_level' => 'required|integer|min:1|max:3',
            'instruments.*.children' => 'sometimes|array',
            'instruments.*.children.*.instrument_name' => 'required|string|max:255',
            'instruments.*.children.*.instrument_description' => 'nullable|string',
            'instruments.*.children.*.instrument_code' => 'required|string|max:50',
            'instruments.*.children.*.instrument_level' => 'required|integer|min:2|max:3',
            'instruments.*.children.*.children' => 'sometimes|array',
            'instruments.*.children.*.children.*.instrument_name' => 'required|string|max:255',
            'instruments.*.children.*.children.*.instrument_description' => 'nullable|string',
            'instruments.*.children.*.children.*.instrument_code' => 'required|string|max:50',
            'instruments.*.children.*.children.*.instrument_level' => 'required|integer|min:3|max:3',
        ]);

        return DB::transaction(function () use ($validated) {
            $latestSurvey = Survey::withTrashed()->orderBy('id', 'desc')->first();
            $nextNumber = $latestSurvey ? (int)substr($latestSurvey->code, 5) + 1 : 1;
            $code = 'SURV-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
            $survey = Survey::create([
                'title' => $validated['title'],
                'description' => $validated['description'] ?? null,
                'code' => $code,
                'status' => $validated['status'],
                'start_date' => $validated['start_date'] ?? null,
                'end_date' => $validated['end_date'] ?? null,
                'link' => '',
                'created_by' => auth()->id(),
            ]);
            
            $uuid = (string) Str::uuid();
            
            $survey->update([
                'uuid' => $uuid,
                'link' => route('survey.public', ['uuid' => $uuid])
            ]);

            foreach ($validated['instruments'] as $instrumentData) {
                $this->createInstrument($survey, $instrumentData);
            }
            $survey->load(['instruments' => function ($query) {
                $query->whereNull('parent_id')
                    ->with(['children' => function ($query) {
                        $query->with(['children' => function ($query) {
                            $query->select('id', 'instrument_name', 'instrument_description', 'instrument_code', 'instrument_level', 'parent_id', 'created_at', 'updated_at');
                        }])
                        ->select('id', 'instrument_name', 'instrument_description', 'instrument_code', 'instrument_level', 'parent_id', 'created_at', 'updated_at');
                    }])
                    ->select('id', 'instrument_name', 'instrument_description', 'instrument_code', 'instrument_level', 'parent_id', 'created_at', 'updated_at');
            }]);

            return redirect()->route('survei-management.index')
                ->with('success', 'Survey berhasil dibuat!');
        });
    }

    /**
     * Create an instrument with its children recursively.
     *
     * @param  \App\Models\Survey  $survey
     * @param  array  $instrumentData
     * @param  int|null  $parentId
     * @return \App\Models\Instrument
     */
    protected function createInstrument(Survey $survey, array $instrumentData, ?int $parentId = null): Instrument
    {
        $instrument = new Instrument([
            'survey_id' => $survey->id,
            'parent_id' => $parentId,
            'instrument_name' => $instrumentData['instrument_name'],
            'instrument_description' => $instrumentData['instrument_description'] ?? null,
            'instrument_code' => $instrumentData['instrument_code'],
            'instrument_level' => $instrumentData['instrument_level'],
            'full_instrument_code' => $parentId
                ? $this->generateFullInstrumentCode($instrumentData['instrument_code'], $parentId)
                : $instrumentData['instrument_code'],
            'created_by' => auth()->id() ?? 1,
        ]);

        $instrument->save();

        if (isset($instrumentData['children']) && is_array($instrumentData['children'])) {
            foreach ($instrumentData['children'] as $childData) {
                $this->createInstrument($survey, $childData, $instrument->id);
            }
        }

        return $instrument;
    }

    protected function collectInstrumentIds(array $instrumentData): array
    {
        $ids = [];
        
        if (isset($instrumentData['id']) && $instrumentData['id'] !== null) {
            $ids[] = $instrumentData['id'];
        }
        
        if (isset($instrumentData['instrument_id']) && $instrumentData['instrument_id'] !== null) {
            $ids[] = $instrumentData['instrument_id'];
        }

        if (isset($instrumentData['children']) && is_array($instrumentData['children'])) {
            foreach ($instrumentData['children'] as $child) {
                $ids = array_merge($ids, $this->collectInstrumentIds($child));
            }
        }
        
        if (isset($instrumentData['instruments']) && is_array($instrumentData['instruments'])) {
            foreach ($instrumentData['instruments'] as $instrument) {
                $ids = array_merge($ids, $this->collectInstrumentIds($instrument));
            }
        }

        if (!empty($ids)) {
            \Log::debug('Collected instrument IDs', [
                'from_data' => [
                    'id' => $instrumentData['id'] ?? null,
                    'name' => $instrumentData['instrument_name'] ?? null
                ],
                'found_ids' => $ids
            ]);
        }

        $result = array_values(array_unique(array_filter($ids)));
        
        return $result;
    }

    /**
     * Generate full instrument code based on parent's full code.
     *
     * @param  string  $code
     * @param  int  $parentId
     * @return string
     */
    protected function generateFullInstrumentCode(string $code, int $parentId): string
    {
        $parent = Instrument::findOrFail($parentId);
        return $parent->full_instrument_code . '.' . $code;
    }

    /**
     * Transform instrument data for the response.
     *
     * @param  \App\Models\Instrument  $instrument
     * @return array
     */
    protected function transformInstrument($instrument)
    {
        return [
            'id' => $instrument->id,
            'name' => $instrument->instrument_name,
            'description' => $instrument->instrument_description,
            'code' => $instrument->instrument_code,
            'level' => $instrument->instrument_level,
            'full_code' => $instrument->full_instrument_code,
            'children' => $instrument->relationLoaded('children') && $instrument->children->isNotEmpty()
                ? $instrument->children->map(function ($child) {
                    return $this->transformInstrument($child);
                })->toArray()
                : []
        ];
    }

    /**
     * Get surveys data for listing with required columns
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getSurveysForListing(Request $request)
    {
        $surveys = Survey::withCount('questions')->get();

        $transformedData = $surveys->map(function ($survey) {
            return [
                'KODE' => $survey->code,
                'JUDUL' => $survey->title,
                'TOTAL_PERTANYAAN' => $survey->questions_count,
                'DIBUAT_PADA' => $survey->created_at->format('d/m/Y H:i'),
                'STATUS' => $survey->status === 'aktif' ? 'aktif' : 'nonaktif',
            ];
        });

        return Inertia::render('SurveiManagement/IndexSurveiManagement', [
            'surveys' => $transformedData
        ]);
    }

    /**
     * Get paginated surveys data with sorting and filtering
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getSurveysData(Request $request)
    {
        $page = $request->input('page') ?? 1;
        $perPage = $request->input('size') ?? 10;
        $allowedSortFields = ['id', 'code', 'title', 'status', 'created_at'];
        $sortField = $request->input('sortField');
        $sortField = in_array($sortField, $allowedSortFields) ? $sortField : 'created_at';

        $rawSortOrder = strtolower($request->input('sortOrder'));
        $sortOrder = in_array($rawSortOrder, ['asc', 'desc']) ? $rawSortOrder : 'desc';
        $search = $request->input('search', '');
        $filters = $request->input('filters') ?? [];
        $decodedFilters = is_string($filters) ? json_decode($filters, true) : $filters;

        $query = Survey::withCount('questions')
            ->when($search, function ($q) use ($search) {
                return $q->where(function ($query) use ($search) {
                    $query->where('title', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%")
                        ->orWhere('code', 'like', "%{$search}%");
                });
            });

        $statusFilter = collect($decodedFilters)->firstWhere('column', 'status');
        $status = $statusFilter['value'] ?? strtolower(trim($request->input('status', 'semua')));

        if (!empty($status) && $status !== 'semua') {
            $query->where('status', $status === 'aktif' ? 'aktif' : 'nonaktif');
        }
        $query->orderBy($sortField, $sortOrder);

        $surveys = $query->paginate($perPage, ['*'], 'page', $page);

        $transformedData = $surveys->map(function ($survey) {
            return [
                'id' => $survey->id,
                'uuid' => $survey->uuid,
                'code' => $survey->code,
                'title' => $survey->title,
                'description' => $survey->description,
                'status' => trim(strtolower($survey->status)),
                'created_at' => $survey->created_at->toDateTimeString(),
                'updated_at' => $survey->updated_at->toDateTimeString(),
                'total_question' => $survey->questions_count,
                'total_instrument' => $this->countInstruments($survey->instruments),
                'link' => $survey->link,
            ];
        });

        return response()->json([
            'page' => $page,
            'pageCount' => $surveys->lastPage(),
            'sortField' => $sortField,
            'sortOrder' => $sortOrder,
            'totalCount' => $surveys->total(),
            'data' => $transformedData->values(),
        ]);
    }

    /**
     * Count total instruments including all levels
     * 
     * @param  \Illuminate\Database\Eloquent\Collection  $instruments
     * @return int
     */
    protected function countInstruments($instruments)
    {
        $count = 0;
        
        foreach ($instruments as $instrument) {
            $count++; // Count current instrument
            if ($instrument->relationLoaded('children') && $instrument->children->isNotEmpty()) {
                $count += $this->countInstruments($instrument->children);
            }
        }
        
        return $count;
    }
    
    /**
     * Get the data of the resource.
     */
    public function getData(Request $request)
    {
        $nested = $request->boolean('nested', true);
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search');

        if ($nested) {
            $query = Survey::with(['instruments' => function ($query) {
                $query->whereNull('parent_id')
                    ->with(['children' => function ($query) {
                        $query->with('children');
                    }]);
            }]);

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%")
                        ->orWhere('code', 'like', "%{$search}%");
                });
            }

            $surveys = $query->paginate($perPage);

            $transformed = $surveys->getCollection()->map(function ($survey) {
                return [
                    'id' => $survey->id,
                    'code' => $survey->code,
                    'title' => $survey->title,
                    'description' => $survey->description,
                    'status' => $survey->status,
                    'link' => $survey->link,
                    'instruments' => $survey->instruments->map(function ($instrument) {
                        return $this->transformInstrument($instrument);
                    })
                ];
            });

            return Inertia::render('SurveiManagement/IndexSurveiManagement', [
                'surveys' => $transformed,
                'pagination' => [
                    'current_page' => $surveys->currentPage(),
                    'last_page' => $surveys->lastPage(),
                    'per_page' => $surveys->perPage(),
                    'total' => $surveys->total(),
                ]
            ]);
        }

        $query = Survey::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%");
            });
        }

        $surveys = $query->paginate($perPage);

        return Inertia::render('SurveiManagement/IndexSurveiManagement', [
            'surveys' => $surveys->items(),
            'pagination' => [
                'current_page' => $surveys->currentPage(),
                'last_page' => $surveys->lastPage(),
                'per_page' => $surveys->perPage(),
                'total' => $surveys->total(),
            ]
        ]);
    }

    /**
     * Toggle survey status
     *
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function toggleStatus($id)
    {
        $survey = Survey::findOrFail($id);

        $survey->update([
            'status' => $survey->status === 'aktif' ? 'nonaktif' : 'aktif'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Status survei berhasil diupdate',
            'data' => [
                'id' => $survey->id,
                'status' => $survey->status
            ]
        ]);
    }

    public function create()
    {
        return Inertia::render('SurveiManagement/CreateSurvey');
    }

    /**
     * Update the specified survey in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        \Log::info('Starting survey update', ['survey_id' => $id, 'request_data' => $request->all()]);
        
        $allData = $request->all();
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'code' => 'required|string|max:50|unique:surveys,code,' . $id,
            'status' => 'required|in:aktif,nonaktif',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'instruments' => 'required|array|min:1',
            'instruments.*.instrument_name' => 'required|string|max:255',
            'instruments.*.instrument_description' => 'nullable|string',
            'instruments.*.instrument_code' => 'required|string|max:50',
            'instruments.*.instrument_level' => 'required|integer|min:1|max:3',
            'instruments.*.children' => 'sometimes|array',
            'instruments.*.children.*.instrument_name' => 'required|string|max:255',
            'instruments.*.children.*.instrument_description' => 'nullable|string',
            'instruments.*.children.*.instrument_code' => 'required|string|max:50',
            'instruments.*.children.*.instrument_level' => 'required|integer|min:2|max:3',
            'instruments.*.children.*.children' => 'sometimes|array',
            'instruments.*.children.*.children.*.instrument_name' => 'required|string|max:255',
            'instruments.*.children.*.children.*.instrument_description' => 'nullable|string',
            'instruments.*.children.*.children.*.instrument_code' => 'required|string|max:50',
            'instruments.*.children.*.children.*.instrument_level' => 'required|integer|min:3|max:3',
            'deleted_instrument_ids' => 'sometimes|array',
            'deleted_instrument_ids.*' => 'integer|exists:instruments,id',
        ]);
        
        if (isset($allData['deleted_instrument_ids'])) {
            $validated['deleted_instrument_ids'] = $allData['deleted_instrument_ids'];
        }
        if (isset($allData['instruments']) && is_array($allData['instruments'])) {
            foreach ($allData['instruments'] as $index => $instrument) {
                if (isset($instrument['id'])) {
                    $validated['instruments'][$index]['id'] = $instrument['id'];
                }
                
                if (isset($instrument['children']) && is_array($instrument['children'])) {
                    foreach ($instrument['children'] as $childIndex => $child) {
                        if (isset($child['id'])) {
                            $validated['instruments'][$index]['children'][$childIndex]['id'] = $child['id'];
                        }
                    }
                }
            }
        }
        
        \Log::info('Validation passed', ['validated_data' => $validated]);

        return DB::transaction(function () use ($validated, $id) {
            $survey = Survey::findOrFail($id);
            $survey->update([
                'title' => $validated['title'],
                'description' => $validated['description'] ?? null,
                'code' => $validated['code'],
                'status' => $validated['status'],
                'start_date' => $validated['start_date'] ?? null,
                'end_date' => $validated['end_date'] ?? null,
            ]);

            if (isset($validated['deleted_instrument_ids']) && is_array($validated['deleted_instrument_ids'])) {
                $deletedCount = Instrument::whereIn('id', $validated['deleted_instrument_ids'])
                    ->where('survey_id', $survey->id)
                    ->delete();
                    
                \Log::info('Deleted instruments', [
                    'count' => $deletedCount,
                    'ids' => $validated['deleted_instrument_ids']
                ]);
            }

        $instrumentIdsToKeep = collect($validated['instruments'])
            ->map(function ($instrument) {
                return $this->collectInstrumentIds($instrument);
            })
            ->flatten()
            ->filter()
            ->unique()
            ->values()
            ->toArray();
            
        $instrumentsToDelete = $survey->instruments()
            ->whereNotIn('id', $instrumentIdsToKeep)
            ->when(isset($validated['deleted_instrument_ids']), function($query) use ($validated) {
                return $query->whereNotIn('id', $validated['deleted_instrument_ids']);
            })
            ->withCount('questions')
            ->get();

            $instrumentsToDelete->each(function($instrument) {
                $deletedQuestions = $instrument->questions()->delete();
                
                $deleted = $instrument->delete();
                
                \Log::info('Deleted instrument and its questions', [
                    'instrument_id' => $instrument->id,
                    'deleted_questions_count' => $deletedQuestions
                ]);
            });

            foreach ($validated['instruments'] as $index => $instrumentData) {
                try {
                    $instrument = $this->updateOrCreateInstrument($survey, $instrumentData);
                } catch (\Exception $e) {
                    \Log::error('Error processing instrument', [
                        'index' => $index,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }

            $survey->load(['instruments' => function ($query) {
                $query->whereNull('parent_id')
                    ->with(['children' => function ($query) {
                        $query->with('children');
                    }]);
            }]);

            $response = [
                'message' => 'Survey updated successfully',
                'survey' => $survey
            ];
            \Log::info('Survey update completed', ['survey_id' => $survey->id]);
            return response()->json($response);
        });
    }

    /**
     * Update or create an instrument with its children recursively.
     *
     * @param  \App\Models\Survey  $survey
     * @param  array  $instrumentData
     * @param  int|null  $parentId
     * @return \App\Models\Instrument
     */
    protected function updateOrCreateInstrument(Survey $survey, array $instrumentData, ?int $parentId = null): Instrument
    {
        $data = [
            'instrument_name' => $instrumentData['instrument_name'],
            'instrument_description' => $instrumentData['instrument_description'] ?? null,
            'instrument_code' => $instrumentData['instrument_code'],
            'instrument_level' => $instrumentData['instrument_level'],
            'parent_id' => $parentId,
            'full_instrument_code' => $parentId
                ? $this->generateFullInstrumentCode($instrumentData['instrument_code'], $parentId)
                : $instrumentData['instrument_code'],
        ];

        if (isset($instrumentData['id']) && is_numeric($instrumentData['id'])) {
            $instrument = Instrument::find($instrumentData['id']);
            
            if ($instrument && $instrument->survey_id == $survey->id) {
                $instrument->update($data + ['updated_by' => auth()->id() ?? 1]);
                
                if (isset($instrumentData['children']) && is_array($instrumentData['children'])) {
                    foreach ($instrumentData['children'] as $childData) {
                        $this->updateOrCreateInstrument($survey, $childData, $instrument->id);
                    }
                }
                
                return $instrument;
            }
        }

        $instrument = Instrument::create($data + [
            'survey_id' => $survey->id,
            'created_by' => auth()->id() ?? 1
        ]);

        if (isset($instrumentData['children']) && is_array($instrumentData['children'])) {
            foreach ($instrumentData['children'] as $childData) {
                $this->updateOrCreateInstrument($survey, $childData, $instrument->id);
            }
        }

        return $instrument;
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $survey = Survey::with(['instruments' => function ($query) {
            $query->whereNull('parent_id')
                ->with(['children' => function ($query) {
                    $query->with('children');
                }]);
        }])->find($id);

        if (!$survey) {
            return response()->json(['message' => 'Survey not found'], 404);
        }

        $transformInstrument = function ($instrument) use (&$transformInstrument) {
            return [
                'id' => $instrument->id,
                'title' => $instrument->instrument_name,
                'description' => $instrument->instrument_description,
                'level' => $instrument->instrument_level,
                'children' => $instrument->relationLoaded('children') && $instrument->children->isNotEmpty()
                    ? $instrument->children->map($transformInstrument)->toArray()
                    : [],
            ];
        };

        return response()->json([
            'id' => $survey->id,
            'title' => $survey->title,
            'description' => $survey->description,
            'code' => $survey->code,
            'status' => $survey->status,
            'is_active' => $survey->status === 'aktif',
            'start_date' => $survey->start_date ? $survey->start_date->format('Y-m-d') : null,
            'end_date' => $survey->end_date ? $survey->end_date->format('Y-m-d') : null,
            'instruments' => $survey->instruments->map($transformInstrument)->toArray(),
        ]);
    }




    /**
     * Remove the specified resource from storage (soft delete).
     *
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(string $id)
    {
        $survey = Survey::findOrFail($id);

        $survey->delete();
        return response()->json(['success' => true, 'message' => 'Survey deleted successfully']);
    }

    /**
     * Restore the specified soft deleted resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function restore(string $id)
    {
        $survey = Survey::withTrashed()->findOrFail($id);

        $survey->restore();

        return redirect()->route('survei-management.index')
            ->with('success', 'Survey restored successfully');
    }

    /**
     * Permanently delete the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function forceDelete(string $id)
    {
        $survey = Survey::withTrashed()->findOrFail($id);

        $survey->forceDelete();

        return redirect()->route('survei-management.trashed')
            ->with('success', 'Survey permanently deleted');
    }

    /**
     * Get a list of soft deleted surveys.
     *
     * @return \Illuminate\Http\Response
     */
    public function trashed()
    {
        $surveys = Survey::onlyTrashed()
            ->with(['instruments' => function ($query) {
                $query->withTrashed();
            }])
            ->get();

        return Inertia::render('SurveiManagement/TrashedSurveys', [
            'surveys' => $surveys
        ]);
    }
}
