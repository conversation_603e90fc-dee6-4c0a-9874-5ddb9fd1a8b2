<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use App\Models\Setting;

class SurveiController extends Controller
{
    /**
     * Show expired survey page
     *
     * @param string $uuid
     * @return \Inertia\Response
     */
    public function showExpired($uuid)
    {
        $survey = \App\Models\Survey::where('uuid', $uuid)->firstOrFail();
        
        if ($survey->isActiveByDate()) {
            return redirect()->route('survey.public', ['uuid' => $survey->uuid]);
        }
        
        return Inertia::render('Errors/SurveyExpired', [
            'survey' => [
                'title' => $survey->title,
                'start_date' => $survey->start_date?->format('d F Y'),
                'end_date' => $survey->end_date?->format('d F Y')
            ]
        ]);
    }

    /**
     * Recursively format instrument and its children
     */
    private function formatInstruments($instruments)
    {
        return $instruments->map(function($instrument) {
            Log::info('Instrument', [
                'id' => $instrument->id,
                'name' => $instrument->instrument_name,
                'questions_count' => $instrument->questions->count(),
                'questions' => $instrument->questions->pluck('question_text')
            ]);
            $data = [
                'id' => $instrument->id,
                'name' => $instrument->instrument_name,
                'description' => $instrument->instrument_description,
                'level' => $instrument->instrument_level,
                'code' => $instrument->instrument_code,
                'respondent_type' => $instrument->respondent_type,
                'questions' => $instrument->questions->sortBy('position')->map(function($question) {
                    $options = $question->q_answer ?? null;
                    $columns = null;
                    $rows = null;
                    $scale = null;

                    if (in_array($question->question_type, ['multiple_choice', 'checkbox', 'dropdown']) && is_array($options)) {
                        $options = collect($options)->map(function($opt, $idx) {
                            return ['id' => $idx + 1, 'text' => $opt];
                        })->toArray();
                    }

                    if (in_array($question->question_type, ['multiple_choice_grid', 'checkbox_grid'])) {
                        $gridSource = is_array($question->q_answer) ? $question->q_answer : [];
                        $columns = collect($gridSource['columns'] ?? [])->map(function($col, $idx) {
                            return ['id' => $idx + 1, 'text' => $col];
                        })->toArray();
                        $rows = collect($gridSource['rows'] ?? [])->map(function($row, $idx) {
                            return ['id' => $idx + 1, 'text' => $row];
                        })->toArray();
                    }
                    if ($question->question_type === 'linear_scale' && is_array($options)) {
                        $scale = $options;
                    }

                    return [
                        'id' => $question->id,
                        'text' => $question->question_text,
                        'type' => $question->question_type,
                        'options' => $options,
                        'columns' => $columns,
                        'rows' => $rows,
                        'scale' => $scale,
                        'answer' => $question->q_answer ?? null,
                        'required' => (bool) $question->required,
                        'position' => (int) $question->position
                    ];
                })->values(),
                'children' => []
            ];

            if ($instrument->relationLoaded('children') && $instrument->children->isNotEmpty()) {
                $data['children'] = $this->formatInstruments($instrument->children);
            }

            return $data;
        })->sortBy('instrument_level')->values();
    }

    /**
     * Show public survey form
     */
    public function showPublic($uuid)
    {
        $survey = \App\Models\Survey::with([
            'instruments' => function($query) {
                return $query->with(['children' => function($q) {
                    return $q->with('questions')
                           ->orderBy('instrument_level')
                           ->orderBy('instrument_code');
                }, 'questions'])
                ->whereNull('parent_id')
                ->orderBy('instrument_level')
                ->orderBy('instrument_code');
            }
        ])->where('uuid', $uuid)
          ->firstOrFail();
            
        if ($survey->status !== 'aktif' && !auth()->check()) {
            return Inertia::render('Errors/SurveyInactive');
        }

        $formattedInstruments = $this->formatInstruments($survey->instruments);
        
        $formattedSurvey = [
            'id' => $survey->id,
            'uuid' => $survey->uuid, 
            'code' => $survey->code,
            'title' => $survey->title,
            'description' => $survey->description,
            'status' => $survey->status,
            'instruments' => $formattedInstruments
        ];
        
        return Inertia::render('SurveiManagement/FormSurvei', [
            'survei' => $formattedSurvey,
            'instruments' => $formattedInstruments,
            'isPublic' => true,
        ]);
    }

    /**
     * Display survey for admin/management
     */
    public function index($id)
    {
        
    }

    public function indexSuccessSubmit()
    {
        $settings = Setting::first();
        $storedSurveys = [];

        if ($settings) {
            $surveyIds = is_string($settings->survey_ids)
                ? json_decode($settings->survey_ids, true) ?? []
                : $settings->survey_ids;

            $storedSurveys = \App\Models\Survey::whereIn('id', (array)$surveyIds)
                ->withCount(['responses as response_count' => function($q) {
                    $q->whereNotNull('submitted_at');
                }])
                ->get()
                ->map(function ($survey) {
                    $totalRespondents = $survey->respondents()->count();
                    $completionRate = $totalRespondents > 0
                        ? round(($survey->response_count / $totalRespondents) * 100)
                        : 0;
                    return [
                        'id' => $survey->id,
                        'code' => $survey->code,
                        'title' => $survey->title,
                        'description' => $survey->description,
                        'link' => $survey->link,
                        'response_count' => $survey->response_count,
                        'completion_rate' => $completionRate,
                    ];
                });
        }

        return Inertia::render('SurveiManagement/SuccessSubmitSurvei', [
            'settings' => $settings ? [
                'title' => $settings->title,
                'description' => $settings->description,
            ] : [
                'title' => null,
                'description' => null,
            ],
            'storedSurveys' => $storedSurveys,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $id)
    {
        return redirect()->route('survei.success-submit-survei');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
