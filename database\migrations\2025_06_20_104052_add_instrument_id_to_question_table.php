<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->foreignId('instrument_id')->nullable()->index('instrument_id');
            $table->foreign(['instrument_id'], 'questions_ibfk_2')->references(['id'])->on('instruments')->onUpdate('restrict')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->dropForeign('questions_ibfk_2');
            $table->dropIndex('instrument_id');
            $table->dropColumn('instrument_id');
        });
    }
};
