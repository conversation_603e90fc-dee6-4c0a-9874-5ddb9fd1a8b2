<?php

namespace Database\Seeders;

use App\Models\RefData;
use App\Models\RefDataDetail;
use Illuminate\Database\Seeder;

class PerguruanTinggiSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // refdata
        $refdata_pt = RefData::firstOrCreate(
            ['title' => 'Perguruan Tinggi seIndonesia'],
            [
                'key'         => 'perguruan-tinggi-seindonesia',
                'description' => 'Data Perguruan Tinggi di seluruh Indonesia',
            ]
        )->id;

        $refdata_prodi = RefData::firstOrCreate(
            ['title' => 'Program Studi Perguruan Tinggi'],
            [
                'key'         => 'prodi-pt',
                'description' => 'Program Studi Perguruan Tinggi',
            ]
        )->id;

        // refdata_details
        $gzFile = base_path('database/seeders/json_file/refdata_pt_details.json.gz');
        $json = gzdecode(file_get_contents($gzFile));
        $rows = json_decode($json, true);

        $ptCount = 0;
        $prodiCount = 0;

        foreach ($rows as $key => $value) {
            if (RefDataDetail::where('ref_code', $value['ref_code'])->exists()) {
                continue;
            }

            $data = [
                'refdata_type' => $value['refdata_type'],
                'ref_code'     => $value['ref_code'],
                'ref_name'     => $value['ref_name'],
                'ref_desc'     => $value['ref_desc'] ?? null,
            ];

            if ($data['refdata_type'] === 'perguruan-tinggi') {
                $data['ref_parent_id'] = null;
                $data['refdata_id'] = $refdata_pt;
                RefDataDetail::create($data);
                $ptCount++;
            } elseif ($data['refdata_type'] === 'program-studi') {
                $data['refdata_id'] = $refdata_prodi;
                $data['ref_parent_id'] = RefDataDetail::where('ref_code', $value['ref_parent_id'])->firstorfail()->id;

                RefDataDetail::create($data);
                $prodiCount++;
            }
        }

        RefData::where('id', $refdata_pt)->update(['items' => $ptCount]);
        RefData::where('id', $refdata_prodi)->update(['items' => $prodiCount]);

        echo "✅ PerguruanTinggiSeeder completed successfully.\n";
        echo "- Inserted {$ptCount} perguruan tinggi and {$prodiCount} program studi.\n";
    }
}
