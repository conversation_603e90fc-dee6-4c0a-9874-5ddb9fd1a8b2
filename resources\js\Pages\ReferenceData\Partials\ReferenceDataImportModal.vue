<script setup lang="ts">
import { ref, defineProps, defineEmits, computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import type { ComponentPublicInstance } from 'vue';
import Toast from '@/Components/Partials/Toast.vue';

const props = defineProps<{
  show: boolean;
  parentKey: string;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'success'): void;
}>();

interface ToastComponent {
  show: (type: string, message: string) => void;
}

const toast = ref<ComponentPublicInstance<ToastComponent> | null>(null);
const fileInputRef = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const importForm = useForm({
  file: null as File | null,
});

const importResult = ref<{
  success: boolean;
  message: string;
  total_success: number;
  total_failed: number;
  total_data: number;
  errors: Array<{
    row: number;
    message: string;
    data: string;
  }>;
} | null>(null);

const hasErrors = computed(() => {
  return importResult.value?.errors && importResult.value.errors.length > 0;
});

const successRate = computed(() => {
  if (!importResult.value || importResult.value.total_data === 0) return 0;
  return Math.round((importResult.value.total_success / importResult.value.total_data) * 100);
});

function resetForm() {
  importForm.reset();
  importForm.clearErrors();
  importResult.value = null;
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }
}

function closeModal() {
  resetForm();
  importForm.cancel();
  emit('close');
}

function showToast(type: 'success' | 'error', message: string) {
  if (toast.value) {
    toast.value.show(type, message);
  }
}

function downloadTemplate() {
  window.location.href = route('reference-data.download-template');
  showToast('success', 'Template berhasil diunduh');
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    processFile(target.files[0]);
    target.value = '';
  }
}

function processFile(file: File) {
  const allowedTypes = [
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'application/vnd.ms-excel.sheet.binary.macroEnabled.12'
  ];
  
  const allowedExtensions = ['.csv', '.xlsx', '.xls'];
  const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
  
  if (!allowedTypes.includes(file.type) && !allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext))) {
    importForm.setError('file', 'File harus berformat CSV, XLS, atau XLSX');
    return;
  }
  
  if (file.size > 10 * 1024 * 1024) {
    importForm.setError('file', 'Ukuran file maksimal 10MB');
    return;
  }
  
  importForm.file = file;
  importForm.clearErrors();
}

function handleDragOver(event: DragEvent) {
  event.preventDefault();
  isDragging.value = true;
}

function handleDragLeave() {
  isDragging.value = false;
}

function handleDrop(event: DragEvent) {
  event.preventDefault();
  isDragging.value = false;
  
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    processFile(event.dataTransfer.files[0]);
  }
}

function submitImport() {
  if (!importForm.file) {
    importForm.setError('file', 'File Import wajib diisi');
    return;
  }

  importResult.value = null;
  
  const formData = new FormData();
  formData.append('file', importForm.file);
  formData.append('_method', 'POST');
  importForm.processing = true;
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
  
  fetch(route('reference-data.import-detail', { key: props.parentKey }), {
    method: 'POST',
    body: formData,
    headers: {
      'X-CSRF-TOKEN': csrfToken,
      'X-XSRF-TOKEN': decodeURIComponent(document.cookie.match(/XSRF-TOKEN=([^;]+)/)?.[1] || ''),
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
  })
  .then(async (response) => {
    const data = await response.json();
    
    if (!response.ok) {
      throw data;
    }
    
    let message = '';
    
    if (data.total_success > 0 && data.total_failed === 0) {
      message = ` Berhasil mengimpor ${data.total_success} data`;
    } else if (data.total_success > 0 && data.total_failed > 0) {
      message = ` Berhasil mengimpor ${data.total_success} dari ${data.total_data} data. ${data.total_failed} data gagal diimpor.`;
    } else if (data.total_success === 0 && data.total_failed > 0) {
      message = ` Gagal mengimpor data. ${data.total_failed} data tidak dapat diproses.`;
    }
    
    importResult.value = {
      success: data.total_success > 0,
      message: message,
      total_success: data.total_success || 0,
      total_failed: data.total_failed || 0,
      total_data: data.total_data || 0,
      errors: data.errors || [],
    };
    
    if (data.total_success > 0) {
      emit('success');
    }
  })
  .catch((error) => {
    console.error('Error importing data:', error);
    
    if (error.errors) {
      importResult.value = {
        success: false,
        message: error.message,
        total_success: error.total_success || 0,
        total_failed: error.total_failed || 0,
        total_data: error.total_data || 0,
        errors: error.errors || [],
      };
      
      if (error.errors.file) {
        importForm.setError('file', error.errors.file);
      }
    } else {
      showToast('error', error.message || 'Terjadi kesalahan pada sistem');
    }
  })
  .finally(() => {
    importForm.processing = false;
  });
}
</script>

<template>
  <!-- Modal Backdrop -->
  <div 
    v-if="show" 
    class="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-black bg-opacity-50 w-full min-h-screen p-4 m-0"
    @click.self="closeModal"
    style="margin: 0;"
  >
    <!-- Modal Container -->
    <div :class="[
      'bg-white shadow-xl mx-auto overflow-hidden',
      importResult ? 'w-full max-w-4xl flex flex-col h-auto max-h-[90vh] rounded-lg' : 'w-full max-w-lg rounded-lg'
    ]">
      <!-- Modal Header -->
      <div :class="[
        'border-b',
        importResult ? 'p-6 bg-gray-50 border-gray-200' : 'p-4'
      ]">
        <h3 :class="[
          'font-semibold',
          importResult ? 'text-xl text-gray-900' : 'text-lg'
        ]">
          Import Detail Data Referensi
        </h3>
      </div>

      <!-- Modal Body -->
      <div :class="[
        importResult ? 'p-6 overflow-y-auto flex-1' : 'p-6'
      ]">
        <div v-if="!importResult" class="space-y-6">
          <!-- File Upload Section -->
          <div 
            class="border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200"
            :class="isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'"
            @dragover.prevent="handleDragOver"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
          >
            <div class="flex flex-col items-center justify-center space-y-3">
              <div v-if="!importForm.file">
                <svg class="w-12 h-12 mx-auto" :class="isDragging ? 'text-blue-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <div class="text-center space-y-1 mt-3">
                  <p class="text-sm text-gray-600">
                    Drag and drop file di sini
                  </p>
                  <p class="text-sm text-gray-600">
                    <span>atau </span>
                    <button 
                      type="button" 
                      @click="fileInputRef?.click()" 
                      class="font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline transition duration-150 ease-in-out"
                    >
                      Upload File
                    </button>
                  </p>
                  <p class="text-xs text-gray-500">
                    Format file: .csv, .xls, .xlsx (maks. 10MB)
                  </p>
                </div>
              </div>
              <div v-else class="w-full">
                <div class="relative mx-auto max-w-xs group">
                  <div class="flex items-center p-3 bg-green-50 rounded-lg border border-green-100 shadow-sm hover:shadow transition-shadow duration-200">
                    <div class="flex-shrink-0 p-2 mr-3 bg-green-100 rounded-full">
                      <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="min-w-0 flex-1">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ importForm.file.name }}
                      </p>
                      <p class="text-xs text-green-600 font-medium">
                        {{ (importForm.file.size / 1024).toFixed(1) }} KB • Ready to import
                      </p>
                    </div>
                    <div class="ml-auto pl-3">
                      <button 
                        type="button" 
                        @click.stop="resetForm"
                        class="inline-flex items-center justify-center w-6 h-6 text-gray-400 hover:text-red-500 rounded-full hover:bg-red-50 transition-colors duration-150"
                        title="Remove file"
                      >
                        <span class="sr-only">Remove file</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <input
                ref="fileInputRef"
                type="file"
                class="hidden"
                accept=".csv,.xls,.xlsx,text/csv"
                @change="handleFileChange"
              >
              <div v-if="importForm.errors.file" class="text-red-500 text-sm mt-1">
                {{ importForm.errors.file }}
              </div>
            </div>
          </div>

          <!-- Template Download Section -->
          <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div class="flex items-start">
              <div class="flex-shrink-0 pt-0.5">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div class="ml-3 flex-1">
                <h3 class="text-sm font-medium text-gray-900">
                  Unduh Template
                </h3>
                <div class="mt-1 text-sm text-gray-600">
                  <p>Dapatkan contoh file untuk mengisi data Anda. File ini berisi urutan kolom dan format yang benar.</p>
                </div>
                <div class="mt-2">
                  <button 
                    type="button" 
                    @click="downloadTemplate"
                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg class="-ml-0.5 mr-1.5 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Unduh
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="importResult" class="space-y-4">
          <!-- Success/Error Alert -->
          <div :class="[
            'border px-4 py-3 rounded relative',
            importResult.success 
              ? 'bg-green-100 border-green-400 text-green-700' 
              : 'bg-red-100 border-red-400 text-red-700'
          ]" role="alert">
            <div class="flex items-center">
              <svg v-if="importResult.success" class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <svg v-else class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              <div>
                <strong class="font-bold">{{ importResult.success ? 'Berhasil!' : 'Gagal!' }}</strong>
                <span class="block sm:inline"> {{ importResult.message }}</span>
              </div>
            </div>
          </div>
          
          <!-- Summary Card - Always shown when there's a result -->
          <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Ringkasan Import</h3>
              
              <!-- Stats -->
              <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div class="px-4 py-3 bg-green-50 rounded-lg">
                  <p class="text-sm font-medium text-gray-500">Berhasil</p>
                  <p class="text-2xl font-semibold text-green-600">{{ importResult.total_success }}</p>
                </div>
                <div class="px-4 py-3" :class="importResult.total_failed > 0 ? 'bg-red-50' : 'bg-gray-50'">
                  <p class="text-sm font-medium text-gray-500">Gagal</p>
                  <p class="text-2xl font-semibold" :class="importResult.total_failed > 0 ? 'text-red-600' : 'text-gray-600'">
                    {{ importResult.total_failed }}
                  </p>
                </div>
                <div class="px-4 py-3 bg-blue-50 rounded-lg">
                  <p class="text-sm font-medium text-gray-500">Total Data</p>
                  <p class="text-2xl font-semibold text-blue-600">{{ importResult.total_data }}</p>
                </div>
              </div>
              
              <!-- Success Rate
              <div v-if="importResult.total_data > 0" class="mt-6">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Persentase Keberhasilan</span>
                  <span>{{ Math.round((importResult.total_success / importResult.total_data) * 100) }}% Berhasil</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div 
                    class="h-2.5 rounded-full" 
                    :class="{
                      'bg-green-500': successRate > 70,
                      'bg-yellow-500': successRate > 30 && successRate <= 70,
                      'bg-red-500': successRate <= 30
                    }"
                    :style="{ width: successRate + '%' }"
                  ></div>
                </div>
              </div> -->
            </div>
            
            <!-- Error List - Only shown when there are errors -->
            <div v-if="hasErrors" class="border-t border-gray-200">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Daftar Kesalahan ({{ importResult.errors.length }})</h3>
                <div class="overflow-x-auto">
                  <div class="max-h-96 overflow-y-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Baris</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pesan Kesalahan</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="(error, index) in importResult.errors" :key="index" class="hover:bg-gray-50">
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ error.row }}</td>
                          <td class="px-6 py-4 text-sm text-red-600">{{ error.message }}</td>
                          <td class="px-6 py-4 text-sm text-gray-500">{{ error.data }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div :class="[
        'bg-gray-50',
        importResult 
          ? 'px-6 py-4 border-t border-gray-200 flex justify-end space-x-3' 
          : 'px-4 py-3 sm:px-6 rounded-b-lg flex justify-end space-x-3 sm:space-x-3'
      ]">
        <template v-if="!importResult">
          <button
            type="button"
            @click="closeModal"
            class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm"
          >
            Batal
          </button>
          <button
            type="button"
            @click="submitImport"
            :class="[
              'inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm',
              'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
            ]"
          >
            <svg v-if="importForm.processing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ importForm.processing ? 'Mengimport...' : 'Import' }}
          </button>
        </template>
        <template v-else>
          <button
            type="button"
            @click="closeModal"
            class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm"
          >
            Tutup
          </button>
          <button
            v-if="!importResult.success"
            type="button"
            @click="importResult = null"
            class="ml-3 inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm"
          >
            Coba Lagi
          </button>
        </template>
      </div>
    </div>
</div>

<!-- Toast Component -->
<Toast ref="toast" />
</template>