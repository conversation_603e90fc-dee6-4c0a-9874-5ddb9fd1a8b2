<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Answer extends Model
{
    use HasFactory;
    
    protected $connection = 'mysql';
    protected $table = 'answers';
    
    protected $fillable = [
        'response_id',
        'question_id',
        'answer',
    ];

    protected $casts = [
        'answer' => 'json',
    ];

    public function response()
    {
        return $this->belongsTo(Response::class, 'response_id');
    }

    public function question()
    {
        return $this->belongsTo(Question::class, 'question_id');
    }
}
