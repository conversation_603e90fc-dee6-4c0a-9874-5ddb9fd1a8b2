<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Response;
use App\Models\Answer;

class Respondent extends Model
{
    use HasFactory;
    
    public $timestamps = false;
    
    protected $connection = 'mysql';
    protected $table = 'respondents';

    protected $fillable = [
        'email',
        'is_member',
        'born_date',
        'job_type',
        'created_at',
        'survey_id',
    ];

    /**
     * Get all responses for the respondent.
     */
    public function responses()
    {
        return $this->hasMany(Response::class);
    }

    /**
     * Get all answers for the respondent.
     */
    public function answers()
    {
        return $this->hasMany(Answer::class);
    }
}

