<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('instruments', function (Blueprint $table) {
            $table->id();
            $table->string('instrument_name');
            $table->text('instrument_description')->nullable();
            $table->string('instrument_code');
            $table->string('instrument_level');
            $table->string('full_instrument_code');
            $table->foreignId('parent_id')->nullable()->constrained('instruments')->onDelete('cascade');
            $table->foreignId('survey_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')
                  ->constrained('users')
                  ->name('fk_instruments_created_by');
            $table->timestamps();
            
            $table->index('parent_id', 'idx_instruments_parent_id');
            $table->index('survey_id', 'idx_instruments_survey_id');
            $table->index('created_by', 'idx_instruments_created_by');
        });
    }

    public function down()
    {
        Schema::dropIfExists('instruments');
    }
};
