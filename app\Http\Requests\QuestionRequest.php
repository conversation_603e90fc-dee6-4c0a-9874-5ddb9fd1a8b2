<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QuestionRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'required' => 'boolean',
            'question' => 'required|string',
            'type' => 'required|string',
            'id_instrument' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'question.required' => 'Question text is required.',
            'type.required' => 'Question type is required.',
            'id_instrument.required' => 'Instrument ID is required.',
        ];
    }

    public function withValidator($validator): void
    {
        $validator->sometimes('q_answer', 'required|array', function () {
            return in_array($this->type, [
                'multiple_choice',
                'multiple_choice_grid',
                'checkbox',
                'checkbox_grid',
                'dropdown',
            ]);
        });

        $validator->sometimes('scale', 'required|array', function () {
            return $this->type === 'linear_scale';
        });

        $validator->sometimes('scale.min', 'required|numeric', function () {
            return $this->type === 'linear_scale';
        });

        $validator->sometimes('scale.max', 'required|numeric', function () {
            return $this->type === 'linear_scale';
        });

        $validator->sometimes('scale.min_label', 'required|string', function () {
            return $this->type === 'linear_scale';
        });

        $validator->sometimes('scale.max_label', 'required|string', function () {
            return $this->type === 'linear_scale';
        });
    }
    
    protected function prepareForValidation(): void
    {
        if ($this->type === 'linear_scale') {
            $this->merge([
                'q_answer' => $this->input('scale')
            ]);
        }
    }
}
