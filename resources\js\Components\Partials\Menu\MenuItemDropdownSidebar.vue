<script setup>
import { ref, onMounted } from 'vue';

defineProps({
    arrow: {
        type: Boolean,
        default: false,
    },
    customMenuLink: {
        type: String,
        default: '',
    },
    active: {
        type: Boolean,
        default: false,
    },
});

const isOpen = ref(false);

onMounted(() => {
    if (localStorage.getItem('sidebar-dropdown-open') === 'true') {
        isOpen.value = true;
    }
});

function toggleDropdown() {
    isOpen.value = !isOpen.value;
    localStorage.setItem('sidebar-dropdown-open', isOpen.value);
}
</script>

<template>
    <div
        :class="'menu-item' + (active ? ' active show' : '')"
        data-menu-item-toggle="dropdown"
    >
        <div :class="'menu-link flex items-center gap-[10px] py-[10px] ps-[10px] pe-[10px] rounded-lg hover:bg-blue-700 transition-colors cursor-pointer group' + (customMenuLink ? ' ' + customMenuLink : '')" @click="toggleDropdown">
            <slot v-if="$slots.icon" name="icon" class="menu-icon" />
            <span
                :class="[
                    'menu-title transition-colors',
                    active ? 'text-white' : 'text-gray-400',
                    'group-hover:text-white hover:text-white'
                ]"
            >
                <slot name="title" />
            </span>
            <span class="ml-auto flex items-center">
                <i v-if="!isOpen" class="ki-filled ki-right text-white/50 text-lg transition-all"></i>
                <i v-else class="ki-filled ki-down text-white/50 text-lg transition-all"></i>
            </span>
            <slot v-if="$slots.arrow" name="arrow" />
            <span v-else-if="arrow" class="menu-arrow flex lg:hidden">
                <i class="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                <i class="ki-filled ki-minus text-2xs hidden menu-item-show:inline-flex"></i>
            </span>
            <slot v-if="$slots.inMenuLink" name="inMenuLink" />
        </div>
        <div v-if="isOpen">
            <slot v-if="$slots.content" name="content" class="menu-dropdown menu-default light:border-gray-300" />
        </div>
    </div>
</template>
