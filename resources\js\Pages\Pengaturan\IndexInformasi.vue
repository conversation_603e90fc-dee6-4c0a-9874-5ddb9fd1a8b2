<script setup lang="ts">
import AppLayout from "@/Layouts/AppLayout.vue";
import { Head } from "@inertiajs/vue3";
import { defineProps, ref, onMounted } from "vue";
import axios from "axios";
import Toast from "@/Components/Partials/Toast.vue";

const props = defineProps({
  responses: {
    type: Array,
    default: () => [],
  },
});
const toastRef = ref<InstanceType<typeof Toast> | null>(null);
interface SurveiPilihanItem {
  id: number;
  code: string;
  title: string;
  description: string;
  total_questions: number;
}

const surveiPilihan = ref<SurveiPilihanItem[]>([]);
const title = ref("");
const description = ref("");
const selectedSurveyIds = ref<number[]>([]);
const loading = ref(false);
const preview = ref({
  title: "",
  description: "",
  surveys: [] as SurveiPilihanItem[],
});

onMounted(async () => {
  try {
    const response = await axios.get("/settings/data");
    surveiPilihan.value = response.data.data.survei_pilihan;
  } catch (e) {
    surveiPilihan.value = [];
  }
});

function toggleSurvey(id: number) {
  if (selectedSurveyIds.value.includes(id)) {
    selectedSurveyIds.value = selectedSurveyIds.value.filter(sid => sid !== id);
  } else {
    selectedSurveyIds.value.push(id);
  }
}

async function handleSubmit(e: Event) {
  e.preventDefault();
  loading.value = true;
  try {
    await axios.post(route('settings.store'), {
      title: title.value,
      description: description.value,
      survey_ids: selectedSurveyIds.value,
    });
    toastRef.value?.show("success", "Berhasil disimpan");
    preview.value.title = title.value;
    preview.value.description = description.value;
    preview.value.surveys = surveiPilihan.value.filter(s => selectedSurveyIds.value.includes(s.id));
  } catch (err) {
    toastRef.value?.show("error", "Gagal disimpan");
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <Head title="Informasi Pasca Submit" />

  <AppLayout
    :data_page="{
      title: 'Informasi Pasca Submit',
    }"
  >
    <div class="mx-auto">
      <div class="space-y-7 pb-6">
        <div class="mb-2">
          <p class="text-gray-700">
            Atur informasi yang akan ditampilkan setelah responden menyelesaikan
            survei
          </p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <form class="space-y-6" @submit="handleSubmit">
            <div>
              <h2 class="text-lg font-semibold text-gray-900 mb-4">Judul</h2>
              <input
                id="title"
                v-model="title"
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Masukkan judul yang akan ditampilkan setelah submit"
                type="text"
              />
            </div>
            <div>
              <h2 class="text-lg font-semibold text-gray-900 mb-4">
                Deskripsi
              </h2>
              <textarea
                id="description"
                v-model="description"
                rows="4"
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Masukkan deskripsi yang akan ditampilkan setelah submit"
              ></textarea>
            </div>
            <div>
              <h2 class="text-lg font-semibold text-gray-900 mb-4">
                Survei Pilihan
              </h2>
              <p class="text-sm text-gray-500 mt-2">
                Pilih satu atau lebih survei aktif yang akan ditampilkan sebagai
                rekomendasi
              </p>
              <div
                class="space-y-2 max-h-60 overflow-y-auto border border-gray-300 rounded-lg p-3"
              >
                <template v-if="surveiPilihan.length">
                  <label
                    v-for="survey in surveiPilihan"
                    :key="survey.id"
                    class="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <input
                      class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      type="checkbox"
                      :value="survey.id"
                      :checked="selectedSurveyIds.includes(survey.id)"
                      @change="toggleSurvey(survey.id)"
                    />
                    <div class="flex-1 min-w-0">
                      <div class="text-sm font-medium text-gray-900">
                        {{ survey.title }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ survey.code }} • {{ survey.description }}
                      </div>
                      <div class="text-xs text-gray-400 mt-1">
                        {{ survey.total_questions }} pertanyaan
                      </div>
                    </div>
                  </label>
                </template>
                <template v-else>
                  <div class="text-gray-400 text-sm">
                    Tidak ada survei aktif.
                  </div>
                </template>
              </div>
            </div>
            <div>
              <h2 class="text-lg font-semibold text-gray-900 mb-4">Preview</h2>
              <div class="bg-gray-50 p-4 rounded-lg border">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ preview.title }}</h3>
                <p class="text-gray-600 mb-3">{{ preview.description }}</p>
                <div class="mt-3" v-if="preview.surveys.length">
                  <p class="text-sm text-gray-700 mb-2">
                    Survei lain yang mungkin menarik:
                  </p>
                  <div class="space-y-2">
                    <div
                      v-for="survey in preview.surveys"
                      :key="survey.id"
                      class="bg-blue-50 p-3 rounded border border-blue-200"
                    >
                      <p class="text-blue-800 font-medium">{{ survey.title }}</p>
                      <p class="text-blue-600 text-sm">{{ survey.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex justify-end pt-4 border-t border-gray-200">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                fdprocessedid="pvda9r"
                :disabled="loading"
              >
                Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <Toast ref="toastRef" />
  </AppLayout>
</template>
