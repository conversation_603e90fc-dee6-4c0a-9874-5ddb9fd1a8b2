<?php

namespace App\Http\Controllers;

use App\Models\Survey;
use App\Models\Setting;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return Inertia::render('Pengaturan/IndexInformasi');
    }

    public function getData()
    {
        $surveys = Survey::withCount('questions')
            ->where('end_date', '>=', now())
            ->orWhereNull('end_date')
            ->get()
            ->map(function ($survey) {
                return [
                    'id' => $survey->id,
                    'code' => $survey->code,
                    'title' => $survey->title,
                    'description' => $survey->description,
                    'total_questions' => $survey->questions_count,
                ];
            });

        $settings = Setting::first();
        $preview = null;

        if ($settings) {
            $surveyIds = is_string($settings->survey_ids) 
                ? json_decode($settings->survey_ids, true) ?? []
                : $settings->survey_ids;
                
            $storedSurveys = Survey::whereIn('id', (array)$surveyIds)
                ->select('code', 'title', 'description')
                ->get()
                ->map(function ($survey) {
                    return [
                        'id' => $survey->id,
                        'code' => $survey->code,
                        'title' => $survey->title,
                        'description' => $survey->description
                    ];
                });

            $preview = [
                'title' => $settings->title,
                'description' => $settings->description,
                'surveys' => $storedSurveys
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'survei_pilihan' => $surveys,
                'preview' => $preview
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Settings/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'survey_ids' => 'required|array',
            'survey_ids.*' => 'exists:surveys,id',
        ]);

        $setting = Setting::updateOrCreate(
            ['id' => 1],
            [
                'title' => $validated['title'],
                'description' => $validated['description'],
                'survey_ids' => json_encode($validated['survey_ids']),
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Settings updated successfully',
            'data' => $setting
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Show specific setting
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return Inertia::render('Settings/Edit', [
            'id' => $id
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Validation and update logic here
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Delete logic here
    }
}
