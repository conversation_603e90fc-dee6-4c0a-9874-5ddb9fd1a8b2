<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Response extends Model
{
    use HasFactory;
    
    public $timestamps = false;
    
    protected $connection = 'mysql';
    protected $table = 'responses';

    protected $fillable = [
        'respondent_id',
        'survey_id',
        'submitted_at',
        'verified_at',
    ];

    protected $casts = [
        'verified_at' => 'datetime',
    ];

    public function respondent()
    {
        return $this->belongsTo(Respondent::class);
    }

    public function survey()
    {
        return $this->belongsTo(Survey::class, 'survey_id')->withDefault([
            'title' => 'Survey Deleted'
        ]);
    }
    
    public function answer()
    {
        return $this->hasMany(Answer::class, 'response_id', 'id');
    }
    
    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::deleting(function ($response) {
            $answers = $response->answer;
            
            foreach ($answers as $answer) {
                if ($answer->answer && is_string($answer->answer)) {
                    if (str_starts_with($answer->answer, 'answers/')) {
                        if (Storage::exists($answer->answer)) {
                            Storage::delete($answer->answer);
                            \Log::info('Deleted file from storage via model event', ['path' => $answer->answer]);
                        }
                    }
                }
            }
        });
    }
}
