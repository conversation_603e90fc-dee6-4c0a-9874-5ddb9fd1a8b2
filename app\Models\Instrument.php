<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Instrument extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'instrument_name',
        'instrument_description',
        'instrument_code',
        'instrument_level',
        'full_instrument_code',
        'parent_id',
        'survey_id',
        'created_by',
        'respondent_type',
    ];

    public function survey(): BelongsTo
    {
        return $this->belongsTo(Survey::class);
    }

    public function children(): HasMany
    {
        return $this->hasMany(Instrument::class, 'parent_id')
            ->with('children.questions', 'questions');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Instrument::class, 'parent_id');
    }

    public function creator(): <PERSON>ongsT<PERSON>
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function questions(): HasMany
    {
        return $this->hasMany(Question::class);
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::deleting(function ($instrument) {
            if ($instrument->isForceDeleting()) {
                $instrument->children()->withTrashed()->each(function ($child) {
                    $child->forceDelete();
                });
            } else {
                $instrument->children->each->delete();
            }
        });

        static::restoring(function ($instrument) {
            $instrument->children()->withTrashed()->get()->each->restore();
        });
    }

    /**
     * Get all child instruments, including trashed ones.
     */
    public function childrenWithTrashed()
    {
        return $this->hasMany(Instrument::class, 'parent_id')->withTrashed();
    }

    /**
     * Get the parent instrument, including trashed ones.
     */
    public function parentWithTrashed()
    {
        return $this->belongsTo(Instrument::class, 'parent_id')->withTrashed();
    }
}
