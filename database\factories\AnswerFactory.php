<?php

namespace Database\Factories;

use App\Models\Answer;
use App\Models\Question;
use App\Models\Response;
use Illuminate\Database\Eloquent\Factories\Factory;

class AnswerFactory extends Factory
{
    protected $model = Answer::class;

    public function definition()
    {
        return [
            'response_id' => Response::factory(),
            'question_id' => Question::factory(),
            'answer' => json_encode($this->faker->sentence())
        ];
    }
}
